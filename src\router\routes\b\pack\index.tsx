import React, { useEffect, useState } from "react";
import {
    create<PERSON>rows<PERSON><PERSON>outer,
    RouterProvider,
    Navigate,
    Outlet,
    useOutlet,
    useOutletContext,
    useLocation,
    useNavigate,
} from "react-router-dom";
import { Badge } from "antd-mobile";

import useStore, { useTheme } from "@/store";

import NavBar from "@/components/nav-bar";
import TabBar, { RouteTabBar } from "@/components/tab-bar";
import CacheRouteContainer from "@/components/cache-route-container";

import BPackLogList from "@/pages/b/pack/log-list";
import BPackLogDetail from "@/pages/b/pack/log-detail";
import BPackScan from "@/pages/b/pack/scan-pack";
import BReportErrorResult from "@/pages/b/report-error-result";
import NoPermissionPage from "@/pages/403";

import iconPack from "@/assets/imgs/tabs/pack.png";
import iconPackSelected from "@/assets/imgs/tabs/pack-selected.png";
import iconLog from "@/assets/imgs/tabs/log.png";
import iconLogSelected from "@/assets/imgs/tabs/log-selected.png";

import { ReactComponent as LogIcon } from "@/assets/imgs/tabs/svg/log.svg";
import { ReactComponent as PackIcon } from "@/assets/imgs/tabs/svg/pack.svg";

import styles from "./index.module.less";

import type { RouteObject } from "react-router-dom";

const PackLayout = () => {
    const [pageTitle, setPageTitle] = useState("");
    const userMenuPermission = useStore((state) => state.userMenuPermission);

    const tabs = [
        {
            key: "pack-scan",
            title: "扫码装箱",
            icon: (active: boolean) => (
                // active ? <img src={iconPackSelected} /> : <img src={iconPack} />,
                <PackIcon
                    style={{
                        color: active ? "var(--primary-color)" : "#E7E7E7",
                    }}
                ></PackIcon>
            ),
        },
        {
            key: "pack-log",
            title: "记录查看",
            icon: (active: boolean) => (
                // active ? <img src={iconLogSelected} /> : <img src={iconLog} />,
                <LogIcon
                    style={{
                        color: active ? "var(--primary-color)" : "#e7e7e7",
                    }}
                ></LogIcon>
            ),
        },
    ];

    if (!userMenuPermission.find((item) => item.perms === "box")) {
        return (
            <div className={`_global_page ${styles.layout}`}>
                <NavBar>{pageTitle}</NavBar>
                <NoPermissionPage></NoPermissionPage>
            </div>
        );
    }

    return (
        <div className={`_global_page ${styles.PackLayout}`}>
            <NavBar>{pageTitle}</NavBar>
            <div className="_global_pageScrollContent">
                <Outlet context={{ setPageTitle }}></Outlet>
            </div>
            <RouteTabBar tabs={tabs} prefix="/b/home/<USER>"></RouteTabBar>
        </div>
    );
};

interface IPackPageWrapperProps {
    title?: string;
    children: React.ReactNode;
}
const PackPageWrapper = ({ children, title = "" }: IPackPageWrapperProps) => {
    const { setPageTitle } = useOutletContext<any>();
    useEffect(() => {
        setPageTitle(title);
    }, [title]);

    return children;
    // return <div>
    //     {children}
    // </div>
};

export default {
    path: "pack",
    element: <PackLayout></PackLayout>,
    children: [
        {
            index: true,
            element: <Navigate to="pack-scan" replace></Navigate>,
        },
        {
            path: "pack-scan",
            element: <CacheRouteContainer></CacheRouteContainer>,
            children: [
                {
                    index: true,
                    element: (
                        <PackPageWrapper title="扫码装箱">
                            <BPackScan></BPackScan>
                        </PackPageWrapper>
                    ),
                },
                {
                    path: "result",
                    element: (
                        <PackPageWrapper title="扫码装箱">
                            <BReportErrorResult></BReportErrorResult>
                        </PackPageWrapper>
                    ),
                },
            ],
        },
        {
            path: "pack-log",
            children: [
                {
                    index: true,
                    element: (
                        <PackPageWrapper title="箱码列表">
                            <BPackLogList></BPackLogList>
                        </PackPageWrapper>
                    ),
                },
                {
                    path: ":id",
                    element: (
                        <PackPageWrapper title="箱码详情">
                            <BPackLogDetail></BPackLogDetail>
                        </PackPageWrapper>
                    ),
                },
            ],
        },
    ],
} as RouteObject;

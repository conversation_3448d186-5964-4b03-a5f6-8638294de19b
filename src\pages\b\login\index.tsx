import { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
    Button,
    Input,
    TextArea,
    Picker,
    Toast,
    Image,
    SpinLoading,
} from "antd-mobile";
import {
    PhoneFill,
    LockFill,
    EyeInvisibleOutline,
    EyeOutline,
} from "antd-mobile-icons";
import { useRequest } from "ahooks";

import request from "@/services";

import { bSideRequests } from "@/services";
import { rsaEncrypt } from "@/utils";

import useStore from "@/store";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";

import iconAccount from "@/assets/imgs/b-login-icon-account.png";
import iconPassword from "@/assets/imgs/b-login-icon-password.png";
import iconSafe from "@/assets/imgs/b-login-icon-safe.png";
import logoNew from "@/assets/imgs/logonew.png";

import styles from "./index.module.less";

export default () => {
    const navigate = useNavigate();
    const login = useStore((state) => state.login);

    const [visible, setVisible] = useState(false);
    const [loginForm] = Form.useForm();
    const initJson: any = {
        title1: "欢迎使用",
        title2: "稻香汤原溯源平台",
        title3: "稻香汤原溯源平台",
    };
    const [titleJson, setTitleJson] = useState(initJson);

    const loginRequest = useRequest(bSideRequests.login, {
        manual: true,
        onSuccess: async (res) => {
            const jwt = res.data.data;
            sessionStorage.setItem("jwt", jwt);
            // const infodata=await
            const infodata = bSideRequests.getUserInfo();
            infodata
                .then((result) => {
                    console.log("信息11111111111111111", result);
                    if (result?.data?.data?.firstFlag === 0) {
                        navigate("/b/editpassword");
                        sessionStorage.setItem("userId", result.data.data.id);
                        sessionStorage.setItem("firstlogin", "1");

                        return;
                    } else {
                        // if (jwt) {

                        login();
                        navigate("/b", {
                            replace: true,
                        });
                        // }
                    }
                })
                .catch((error) => {
                    // 处理错误
                    console.error(error);
                });
            if (!jwt) {
                Toast.show({
                    content: "获取token失败",
                });
            }
        },
        onError() {
            refreshGraphicVerificationCode();
        },
    });

    const loginTitleInfo = useRequest(bSideRequests.getTitle, {
        manual: true,
        onSuccess(response: any) {
            const res = response?.data;
            console.log(res);
            if (res.code === 200) {
                const json = res?.data[0];
                setTitleJson(json);
                return;
            }
        },
        onError() {
            refreshGraphicVerificationCode();
        },
    });

    useEffect(() => {
        loginTitleInfo.run({
            titleType: 1,
        });
    }, []);
    const xRequestIdRef = useRef(null);
    const [graphicVerificationCodeUrl, setGraphicVerificationCodeUrl] =
        useState<string | undefined>(undefined);
    const getGraphicVerificationCodeRequest = useRequest(
        bSideRequests.getGraphicVerificationCode,
        {
            onSuccess(res) {
                const xRequestId = res.headers["x-request-id"];
                xRequestIdRef.current = xRequestId;
                const url = URL.createObjectURL(res.data);
                setGraphicVerificationCodeUrl(url);
            },
            onError(err: any) {
                const errBlob = err?.response?.data;
                if (errBlob?.type === "application/json") {
                    const reader = new FileReader();
                    reader.onload = function () {
                        const text = reader.result as string;
                        const json = JSON.parse(text);
                        const errMessage = json.message;
                        Toast.show(errMessage);
                    };
                    reader.readAsText(errBlob);
                } else {
                    Toast.show("获取验证码失败");
                }
            },
        },
    );
    const refreshGraphicVerificationCode = () => {
        getGraphicVerificationCodeRequest.refresh();
        loginForm.resetFields(["verificationCode"]);
    };

    const renderGraphicVerificationCode = () => {
        if (getGraphicVerificationCodeRequest.loading) {
            return (
                <SpinLoading
                    style={{
                        "--size": "20px",
                    }}
                ></SpinLoading>
            );
        }
        return (
            <Image
                style={{
                    maxWidth: 150,
                }}
                onClick={() => {
                    refreshGraphicVerificationCode();
                }}
                src={graphicVerificationCodeUrl}
                alt="验证码"
            />
        );
    };

    return (
        <div className={`_global_page ${styles.login}`}>
            <div className="main login_input--style">
                <div className={styles.leftImgFont}>
                    {/* <p className="pLine"></p> */}
                    <p>{titleJson.title1}</p>
                    <p>{titleJson.title2}</p>
                </div>
                <div className="title">
                    {titleJson.title3}
                    <img className={styles.titleIcon} src={logoNew} alt="" />
                </div>
                <Form
                    form={loginForm}
                    layout="horizontal"
                    onFinishFailed={(failInfo) => {
                        const errorMsg =
                            failInfo?.errorFields?.[0]?.errors?.[0];
                        Toast.show(errorMsg);
                    }}
                    onFinish={async (values) => {
                        console.log(values);
                        const pKRet = await request({
                            method: "get",
                            url: "/sys-config/getPublicKey",
                            data: {},
                        });

                        const pK = pKRet?.data?.data || "";
                        // debugger
                        loginRequest.run({
                            phoneNumber: await rsaEncrypt(values.phone, pK),
                            password: await rsaEncrypt(values.password, pK),
                            imageCode: values.verificationCode,
                            xRequestId: xRequestIdRef.current,
                            // t:pK // 查看public Key
                        });
                    }}
                >
                    <div className="formArea ">
                        <div className="formItem">
                            <Image
                                className="formItem__label"
                                src={iconAccount}
                            ></Image>
                            <Form.Item
                                name="phone"
                                noStyle
                                validateFirst
                                rules={[
                                    {
                                        required: true,
                                        message: "手机号不能为空",
                                    },
                                    {
                                        pattern: /^[1][3,4,5,6.7,8,9][0-9]{9}$/,
                                        message: "请输入正确的手机号",
                                    },
                                ]}
                            >
                                <Input
                                    className="formItem__input"
                                    type="tel"
                                    placeholder="手机号码"
                                />
                            </Form.Item>
                            <Form.Item noStyle shouldUpdate>
                                {() => {
                                    const { errors = [] } =
                                        loginForm
                                            .getFieldsError()
                                            .find((item) => {
                                                return item.name[0] === "phone";
                                            }) || {};
                                    return (
                                        errors.length > 0 && (
                                            <div className="errInfo">
                                                <div className="formItem__label"></div>
                                                {errors[0]}
                                            </div>
                                        )
                                    );
                                }}
                            </Form.Item>
                        </div>
                        <div className="formItem">
                            <Image
                                className="formItem__label"
                                src={iconPassword}
                            ></Image>
                            <Form.Item
                                name="password"
                                noStyle
                                rules={[
                                    { required: true, message: "密码不能为空" },
                                ]}
                            >
                                <Input
                                    className="formItem__input"
                                    type={visible ? "text" : "password"}
                                    placeholder="登录密码"
                                />
                            </Form.Item>
                            <div className="eye">
                                {!visible ? (
                                    <EyeInvisibleOutline
                                        onClick={() => setVisible(true)}
                                    />
                                ) : (
                                    <EyeOutline
                                        onClick={() => setVisible(false)}
                                    />
                                )}
                            </div>
                            <Form.Item noStyle shouldUpdate>
                                {() => {
                                    const { errors = [] } =
                                        loginForm
                                            .getFieldsError()
                                            .find((item) => {
                                                return (
                                                    item.name[0] === "password"
                                                );
                                            }) || {};
                                    return (
                                        errors.length > 0 && (
                                            <div className="errInfo">
                                                <div className="formItem__label"></div>
                                                {errors[0]}
                                            </div>
                                        )
                                    );
                                }}
                            </Form.Item>
                        </div>
                        <div className="formItem">
                            <Image
                                className="formItem__label"
                                src={iconSafe}
                            ></Image>
                            <Form.Item
                                name="verificationCode"
                                noStyle
                                rules={[
                                    {
                                        required: true,
                                        message: "验证码不能为空",
                                    },
                                ]}
                            >
                                <Input
                                    className="formItem__input"
                                    type="text"
                                    placeholder="验证码"
                                />
                            </Form.Item>
                            <div className="graphicVerificationCodeContainer">
                                {renderGraphicVerificationCode()}
                            </div>
                            <Form.Item noStyle shouldUpdate>
                                {() => {
                                    const { errors = [] } =
                                        loginForm
                                            .getFieldsError()
                                            .find((item) => {
                                                return (
                                                    item.name[0] ===
                                                    "verificationCode"
                                                );
                                            }) || {};
                                    return (
                                        errors.length > 0 && (
                                            <div className="errInfo">
                                                <div className="formItem__label"></div>
                                                {errors[0]}
                                            </div>
                                        )
                                    );
                                }}
                            </Form.Item>
                        </div>
                    </div>

                    {/* <div className="btn_bg"> */}
                    <Button
                        className="loginBtn"
                        loading={loginRequest.loading}
                        block
                        fill="none"
                        // style={{ '--background-color': 'transparent' }}
                        type="submit"
                        color="primary"
                    >
                        登录
                    </Button>
                    {/* </div> */}
                    {/* <Button
            className="loginBtn"
            loading={loginRequest.loading}
            block
            fill='none'
          // type="submit"
          // // color="primary"
          >
            登录
          </Button> */}
                </Form>
            </div>
        </div>
    );
};

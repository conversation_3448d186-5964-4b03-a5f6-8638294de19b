import React, { useState } from "react";
import {
    Image,
    ImageViewer,
    Modal,
    Ellipsis,
    Button,
    Toast,
} from "antd-mobile";
import copyToClipboard from "copy-to-clipboard";

import { showConfirmModal } from "@/utils";

import styles from "./index.module.less";

interface IEllipsisTraceCodeProps {
    codeText?: string;
}
function EllipsisTraceCode(props: IEllipsisTraceCodeProps) {
    const { codeText } = props;

    const handleShowCode = () => {
        showConfirmModal({
            title: "查看",
            content: codeText,
            confirmText: "复制",
            onConfirm() {
                const copyRet = copyToClipboard(codeText!);
                if (copyRet) {
                    Toast.show("复制成功");
                } else {
                    Toast.show({
                        icon: "fail",
                        content: "辅助失败",
                    });
                }
            },
        });
    };

    if (typeof codeText !== "string") {
        return "-";
    }

    return (
        <div className={styles.EllipsisTraceCode}>
            <Ellipsis
                direction="middle"
                className={styles.ellipsisText}
                content={codeText}
            ></Ellipsis>
            <Button
                className={styles.showBtn}
                color="primary"
                size="mini"
                fill="none"
                onClick={handleShowCode}
            >
                查看
            </Button>
        </div>
    );
}

export default EllipsisTraceCode;

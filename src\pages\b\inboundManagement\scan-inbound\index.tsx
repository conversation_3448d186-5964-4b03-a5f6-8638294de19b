import { useState, useCallback, useRef, useEffect } from "react";
import { useNavigate, useLocation, useParams } from "react-router-dom";
import { Button, Toast, Modal, Dialog } from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
    UpOutline,
    DownOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import useUrlState from "@ahooksjs/use-url-state";
import { useImmer } from "use-immer";
import { useRequest, useSetState } from "ahooks";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";
import CollapseTable from "@/components/collapse-table";
import CheckListPopup from "@/components/check-list-popup";
import BoxCodeScanner from "@/components/box-code-scanner";
import BoxCodeListScanner from "@/components/box-code-scanner-list";

import iconScanQrCode from "@/assets/imgs/scan-qrcode.png";
import { ReactComponent as SvgScanQrCode } from "@/assets/imgs/scan-qrcode.svg";

import { REPOSITORY_TYPE_CONSTANTS, MAX_SCAN_LENGTH } from "@/config";
import { useConfirmBlock, useScanQRCode } from "@/hooks";
import {
    bSideRequests,
    getPackCodeInfoByPackCode,
    BoxCodeVerifyModule,
} from "@/services";
import { showToastLoading, showConfirmModal, requestDataSign } from "@/utils";

import styles from "./index.module.less";

import type {
    unstable_Blocker as Blocker,
    unstable_BlockerFunction as BlockerFunction,
} from "react-router-dom";
import type { ToastLoadingHandler } from "@/utils";
import { getParamUsingURLSearchParams } from "@/utils";
import type { BoxCodeData } from "@/components/box-code-scanner";
import type { BoxCodeDataList } from "@/components/box-code-scanner-list";

const ScanInbound = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const [visible, setVisible] = useState(false);
    const [urlState, setUrlState] = useUrlState<{
        step: "form" | "scan";
    }>({
        step: "form",
    });
    const [ids, setIds] = useState("");

    useEffect(() => {
        if (location.search !== "?step=form" && location.search) {
            navigate(location.pathname + location.search, {
                replace: true,
            });
        } else {
            navigate(location.pathname + "?step=form", {
                replace: true,
            });
        }
        const urlId = getParamUsingURLSearchParams(location.search, "id") || "";
        console.log(urlId, "urlId");
        setIds(urlId);
    }, []);
    const [proId, setProId] = useState(0);
    const [proValue, setProValue] = useState("");

    const [pubId, setPubId] = useState("");
    const [pubValue, setPubValue] = useState("");

    const [inboundInfoForm] = Form.useForm();

    const [scannedBoxCodeInfo, setScannedBoxCodeInfo] =
        useState<BoxCodeDataList>([]);

    // 添加入库数量状态
    const [amount, setAmount] = useState<number>(1);

    const toastLoadingHandlerRef = useRef<ToastLoadingHandler | null>(null);
    const inboundScanEntryRequest = useRequest(bSideRequests.wareProcessEntry, {
        manual: true,
        onSuccess() {
            toastLoadingHandlerRef?.current?.close?.();
            inboundInfoForm.resetFields();
            setScannedBoxCodeInfo([]);
            setAmount(1); // 重置入库数量
            Toast.show({
                icon: "success",
                content: "上报成功",
            });
            isFormFinished.current = true;
            if (ids) {
                navigate(-1);
            }
        },
        onError(ret: any) {
            toastLoadingHandlerRef?.current?.close?.();
            const errMsg = ret?.response?.data?.message || "上报失败";
            isFormFinished.current = true;
            navigate(`result?msg=${errMsg}`);
        },
    });

    // 获取仓储名称列表
    const selectInboundList = useRequest(bSideRequests.selectInboundList, {
        manual: false,
        defaultParams: [{ valid: "true" }], // 默认参数
        onSuccess() {
            // toastLoadingHandlerRef?.current?.close?.();
            // setIsFormFinished(true);
            // Toast.show("上报成功");
            // myFormRef.resetFields();
        },
    });
    const ad =
        selectInboundList?.data?.data?.data?.map?.((obj: any) => ({
            label: obj.warehouseName,
            value: obj.id,
        })) || [];

    const productNameOptions = proValue
        ? [{ label: proValue || "", value: proId || "" }, ...ad].reduce(
              (acc, current) => {
                  const isDuplicate = acc.some(
                      (item: any) => item.value === current.value,
                  );
                  if (!isDuplicate) {
                      acc.push(current);
                  }
                  return acc;
              },
              [],
          )
        : [...ad];

    // 获取生产批次列表
    const selectBatchList = useRequest(bSideRequests.selectBatchList, {
        manual: true,
        onSuccess() {
            // toastLoadingHandlerRef?.current?.close?.();
            // setIsFormFinished(true);
            // Toast.show("上报成功");
            // myFormRef.resetFields();
        },
    });
    const selectBatchArr =
        selectBatchList?.data?.data?.data?.map?.((obj: any) => ({
            label: obj.productionBatch,
            value: obj.id,
        })) || [];

    const selectBatchOptions = pubValue
        ? [
              { label: pubValue || "", value: pubId || "" },
              ...selectBatchArr,
          ].reduce((acc, current) => {
              const isDuplicate1 = acc.some(
                  (item: any) => item.value === current.value,
              );
              if (!isDuplicate1) {
                  acc.push(current);
              }
              return acc;
          }, [])
        : [...selectBatchArr];

    const handleProductChange = (value: any) => {
        // 调用第二个接口，并传入第一个接口返回的 id
        inboundInfoForm.setFieldsValue({ productionId: undefined }); // 清空生产批次
        setPubValue("");
        if (value[0]) {
            selectBatchList.run({
                id: value[0],
            });
        }
    };

    const isFormFinished = useRef(true);
    const shouldBlock = useCallback<BlockerFunction>(
        ({ currentLocation, nextLocation }) => {
            return (
                !isFormFinished.current &&
                currentLocation.pathname !== nextLocation.pathname
            );
        },
        [],
    );
    useConfirmBlock({
        content: "离开后编辑内容不保存，确认离开吗？",
        shouldBlock: shouldBlock,
    });
    // inboundInfoForm.setFieldsValue({
    //     productId: ["0"],
    // });

    const renderInboundForm = (isShow: boolean) => {
        return (
            <div
                style={{
                    display: isShow ? "block" : "none",
                }}
            >
                <Form
                    form={inboundInfoForm}
                    layout="horizontal"
                    onFinish={(values) => {
                        // setUrlState({
                        //     step: "scan",
                        // });
                    }}
                    onFinishFailed={(failInfo) => {
                        const errorMsg =
                            failInfo?.errorFields?.[0]?.errors?.[0];
                        Toast.show(errorMsg);
                    }}
                    onValuesChange={() => {
                        isFormFinished.current = false;
                    }}
                >
                    <Form.Item
                        name="warehouseName"
                        label="仓库名称"
                        style={{
                            // @ts-ignore
                            "--form-item-label-font-size": "18px",
                        }}
                        rules={[
                            {
                                required: true,
                                message: "点击选择仓库名称", // 自定义错误提示信息
                            },
                        ]}
                    >
                        <CheckListPopup
                            title="选择仓库名称"
                            placeholder="点击选择仓库名称"
                            items={productNameOptions}
                            // onChange={(e: any) => handleProductChange(e)}
                        ></CheckListPopup>
                    </Form.Item>

                    <Form.Item>
                        <div>
                            <BoxCodeListScanner
                                module={BoxCodeVerifyModule.IN}
                                value={scannedBoxCodeInfo}
                                onChange={(data) => {
                                    console.log(data, "data");
                                    setScannedBoxCodeInfo(data);
                                }}
                                onQuantityChange={(quantity) => {
                                    console.log(
                                        "父组件接收到数量变化:",
                                        quantity,
                                    );
                                    setAmount(quantity);
                                    console.log(
                                        "父组件amount状态已更新为:",
                                        quantity,
                                    );
                                }}
                            ></BoxCodeListScanner>
                            <Button
                                loading={inboundScanEntryRequest.loading}
                                onClick={async () => {
                                    if (scannedBoxCodeInfo.length === 0) {
                                        Toast.show("请扫描产品溯源码");
                                        return;
                                    }
                                    try {
                                        // 手动验证表单
                                        await inboundInfoForm.validateFields();
                                        showConfirmModal({
                                            title: "确认上传",

                                            onConfirm() {
                                                handleEntryRequest();
                                            },
                                        });
                                    } catch (error) {
                                        // 验证失败，会触发 onFinishFailed 中的逻辑
                                    }
                                }}
                                className={`${styles.Btn}`}
                                fill="none"
                                color="primary"
                                shape="rounded"
                                block
                                type="submit"
                            >
                                上报
                            </Button>
                        </div>
                    </Form.Item>
                </Form>
            </div>
        );
    };

    const handleEntryRequest = async () => {
        const inboundInfo = inboundInfoForm.getFieldsValue();
        console.log(inboundInfo, scannedBoxCodeInfo, "handleEntryRequest");
        console.log("当前入库数量:", amount); // 添加调试信息
        try {
            // 获取扫描的产品信息
            const firstScannedItem = scannedBoxCodeInfo[0];
            console.log(firstScannedItem, "firstScannedItem");

            const requestData = {
                // 添加扫描获取的信息
                warehouseInfoId: inboundInfo?.warehouseName[0], // 仓库名称
                orgName: firstScannedItem?.orgName, // 生产加工企业
                productName: firstScannedItem?.productName, // 产品名称
                productionBatch: firstScannedItem?.productionBatch, // 生产批次
                inboundNum: amount, // 使用用户修改后的入库数量
            } as const;
            console.log("请求数据:", requestData); // 添加调试信息
            const signedData = await requestDataSign(
                requestData,
                "addWarehouseVo",
            );
            toastLoadingHandlerRef.current = showToastLoading({
                duration: 0,
                content: "正在上传",
            });
            inboundScanEntryRequest.run(signedData);
        } catch (err) {
            console.log(err, "err");
            Toast.show({
                icon: "fail",
                content: err?.toString?.() || "加密失败",
            });
        }
    };

    // const renderScanView = () => {
    //     return (
    //         <div>
    //             <Form
    //                 form={inboundInfoForm}
    //                 layout="horizontal"
    //                 onFinish={(values) => {
    //                     // setUrlState({
    //                     //     step: "scan",
    //                     // });
    //                 }}
    //                 onFinishFailed={(failInfo) => {
    //                     const errorMsg =
    //                         failInfo?.errorFields?.[0]?.errors?.[0];
    //                     Toast.show(errorMsg);
    //                 }}
    //                 onValuesChange={() => {
    //                     isFormFinished.current = false;
    //                 }}
    //             >
    //                 <Form.Item
    //                     name="productId"
    //                     label="产品名称"
    //                     rules={[
    //                         {
    //                             required: true,
    //                             message: "请选择产品名称", // 自定义错误提示信息
    //                         },
    //                     ]}
    //                 >
    //                     <CheckListPopup
    //                         title="选择产品名称"
    //                         placeholder="请选择"
    //                         items={productNameOptions}
    //                         onChange={(e: any) => handleProductChange(e)}
    //                     ></CheckListPopup>
    //                 </Form.Item>

    //                 <Form.Item
    //                     name="productionId"
    //                     label="生产批次"
    //                     rules={[
    //                         {
    //                             required: true,
    //                             message: "请选择生产批次", // 自定义错误提示信息
    //                         },
    //                     ]}
    //                 >
    //                     <CheckListPopup
    //                         title="选择生产批次"
    //                         placeholder="请选择"
    //                         items={selectBatchOptions}
    //                     ></CheckListPopup>
    //                 </Form.Item>

    //                 <Form.Item>
    //                     <Button
    //                         loading={inboundScanEntryRequest.loading}
    //                         onClick={async () => {
    //                             try {
    //                                 // 手动验证表单
    //                                 await inboundInfoForm.validateFields();
    //                                 showConfirmModal({
    //                                     title: "编辑溯源码信息",
    //                                     content: "请确认是否修改溯源码信息?",
    //                                     onConfirm() {
    //                                         handleEntryRequest();
    //                                     },
    //                                 });
    //                             } catch (error) {
    //                                 // 验证失败，会触发 onFinishFailed 中的逻辑
    //                             }
    //                         }}
    //                         style={{ marginTop: 100 }}
    //                         className={`${styles.Btn}`}
    //                         fill="none"
    //                         color="primary"
    //                         shape="rounded"
    //                         block
    //                     >
    //                         上报
    //                     </Button>
    //                 </Form.Item>
    //             </Form>
    //         </div>
    //     );
    // };

    return (
        <div className={`${styles.scanInbound}`}>
            {renderInboundForm(urlState.step === "form")}
            {urlState.step === "scan"}
        </div>
    );
};

export default ScanInbound;

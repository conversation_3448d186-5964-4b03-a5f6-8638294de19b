.boxCodeScanner {
    // border-bottom: 1px solid var(--support-color);
    padding-bottom: 10px;
    position: relative;
    width: 103%;
    left: -2%;
    .deleteBtn {
        margin: 0 auto;
        padding: 0 0;
        font-size: var(--text-sm);
        text-align: right;
        width: 150px;
        color: #fc595d;
        cursor: pointer;
    }
    .scanBtn {
        box-sizing: content-box;
        margin-top: 20px;
        margin-left: 18px;
        // padding: 6px 5px;
        border-radius: calc(var(--border-radius) / 2);
        width: 245px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 18px;
        line-height: 1;
        & > span {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .scanBtnIcon {
            font-size: 18px;
        }
    }
    .scanFrom:nth-child(1) {
        font-size: var(--form-item-label-font-size);
        display: flex;
        justify-content: space-between;
        padding: 0 0 10px 0;
        position: relative;

        & > div:first-child {
            flex-shrink: 0;
            min-width: 100px;
            white-space: nowrap;
            margin-right: 10px;
        }

        & > div:last-child {
            flex: 1;
            text-align: right;
            word-break: break-all;
        }
    }
    .scanFrom {
        font-size: var(--form-item-label-font-size);
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        position: relative;

        & > div:first-child {
            flex-shrink: 0;
            min-width: 100px;
            white-space: nowrap;
            margin-right: 10px;
        }

        & > div:last-child {
            // flex: 1;
            // text-align: right;
            // word-break: break-all;
        }
    }
    .scanFrom::after {
        content: "";
        position: absolute;
        left: -20px; /* 向左延伸20px */
        right: -20px; /* 向右延伸20px */
        bottom: 0;
        border-bottom: var(--border-inner);
    }

    .warmTip {
        margin: 10px 0;
        border-radius: 4px;
        font-size: 10px;
        color: #666;
        line-height: 1.4;
    }
}

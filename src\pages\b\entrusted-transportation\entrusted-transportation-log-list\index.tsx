import { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
    Button,
    Input,
    <PERSON><PERSON><PERSON>,
    Picker,
    InfiniteScroll,
    Toast,
    <PERSON>rror<PERSON><PERSON>,
    DotLoading,
    PullToRefresh,
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import { useRequest, useInfiniteScroll, useDebounce } from "ahooks";
import useUrlState from "@ahooksjs/use-url-state";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";

import {
    INFINITE_LIST_PAGE_SIZE,
    TRANSPORTATION_STATE_CONSTANTS,
    TRANSPORTATION_TYPE_CONSTANTS,
} from "@/config";
import { bSideRequests } from "@/services";

import styles from "./index.module.less";

type ListType = "NORMAL" | "DEPRECATED";
export default () => {
    const navigate = useNavigate();

    const [urlState, setUrlState] = useUrlState<{
        listType: ListType;
    }>(
        {
            listType: "NORMAL",
        },
        {
            navigateMode: "replace",
        },
    );

    const getListRequest = useInfiniteScroll(
        (d) => {
            const page = d
                ? Math.ceil(d.list.length / INFINITE_LIST_PAGE_SIZE) + 1
                : 1;

            const stateMap = {
                NORMAL: TRANSPORTATION_STATE_CONSTANTS.TRANSPORTATION_STATE_KV
                    .COMPLETE,
                DEPRECATED:
                    TRANSPORTATION_STATE_CONSTANTS.TRANSPORTATION_STATE_KV
                        .DEPRECATED,
            } as const;

            return bSideRequests
                .getTransportationList({
                    type: 2,
                    pageIndex: page,
                    pageSize: INFINITE_LIST_PAGE_SIZE,
                    state: stateMap[urlState.listType as ListType],
                })
                .then((res) => {
                    const listInfo = res?.data?.data || {};
                    return {
                        list: listInfo?.records || [],
                        total: listInfo?.total || 0,
                    };
                });
        },
        {
            reloadDeps: [urlState.listType],
        },
    );
    const hasMore =
        getListRequest.data &&
        getListRequest.data.list.length < getListRequest.data.total;

    const data = getListRequest?.data?.list || [];

    const renderList = () => {
        if (getListRequest.loading) {
            return (
                <div
                    style={{
                        paddingTop: "30px",
                        textAlign: "center",
                        color: "var(--adm-color-weak)",
                    }}
                >
                    <div style={{ fontSize: 24, marginBottom: 24 }}>
                        <DotLoading />
                    </div>
                    正在加载数据
                </div>
            );
        }
        if (data.length === 0) {
            return (
                <ErrorBlock
                    image={<div></div>}
                    title="暂无数据"
                    description=""
                />
            );
        }
        return (
            <div>
                <PullToRefresh
                    onRefresh={async () => {
                        await getListRequest.reloadAsync();
                    }}
                >
                    <List className="transportationLogList">
                        {data.map((item) => {
                            return (
                                <List.Item
                                    key={item?.id}
                                    onClick={() => {
                                        navigate(item?.id?.toString());
                                    }}
                                >
                                    <div className="transportationLogList__item">
                                        <div>{item.transNumber || "-"}</div>
                                        <div className="transportationLogList__item--time">
                                            {" "}
                                            {item.createTime
                                                ? dayjs(item.createTime).format(
                                                      "YYYY-MM-DD HH:mm:ss",
                                                  )
                                                : "-"}
                                        </div>
                                    </div>
                                </List.Item>
                            );
                        })}
                    </List>
                </PullToRefresh>
                <InfiniteScroll
                    loadMore={async () => {
                        await getListRequest.loadMoreAsync();
                    }}
                    hasMore={hasMore || false}
                />
            </div>
        );
    };

    return (
        <div className={`${styles.selfTransportationLogList}`}>
            <CapsuleTabs
                activeKey={urlState.listType}
                onChange={(key: any) => {
                    setUrlState({
                        listType: key,
                    });
                }}
            >
                <CapsuleTabs.Tab title="正常" key="NORMAL"></CapsuleTabs.Tab>
                <CapsuleTabs.Tab
                    title="已作废"
                    key="DEPRECATED"
                ></CapsuleTabs.Tab>
            </CapsuleTabs>
            {renderList()}
        </div>
    );
};

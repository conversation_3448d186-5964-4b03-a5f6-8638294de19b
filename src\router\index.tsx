import { useRequest } from "ahooks";
import { DotLoading } from "antd-mobile";
import { useEffect } from "react";
import {
    createBrowser<PERSON><PERSON>er,
    RouterProvider,
    Navigate,
    Outlet,
} from "react-router-dom";

import { bSideRequests } from "@/services";
import useStore, { useTheme } from "@/store";

import BHome from "@/pages/b/home";
import BLogin from "@/pages/b/login";
import Bpersonalinfo from "@/pages/b/personalinfo";
import Bchangepassword from "@/pages/b/changepassword";
import Bchangephone from "@/pages/b/changephone";
import Beditpassword from "@/pages/b/editpassword";
import Bcode from "@/pages/b/code";

import CTrace from "@/pages/c/trace";
import CCertificate from "@/pages/c/certificate";
import CScanLog from "@/pages/c/scan-log";
import CTraceErrorResult from "@/pages/c/trace-error-result";
import CHome from "../pages/c/trace/home";

import CacheRouteContainer from "@/components/cache-route-container";

import themeBlueStyles from "@/themes/blue.module.less";
import themeGrayStyles from "@/themes/gray.module.less";
import themeGreenStyles from "@/themes/green.module.less";

import bEntrustedTransportationRoute from "./routes/b/entrusted-transportation";
import bInboundRoute from "./routes/b/inbound";
import bOutboundRoute from "./routes/b/outbound";
import bPackRoute from "./routes/b/pack";
import bProductiveProcessRoute from "./routes/b/productive-process";
import bPurchaseRoute from "./routes/b/purchase";
import bInboundManagement from "./routes/b/inboundManagement";
import bSelfTransportationRoute from "./routes/b/self-transportation";

import { LastLocationProvider } from "@/hooks/use-last-location";

import type { ThemeState } from "@/store";
import { decryptStr } from "@/utils";

const ThemeVariableInject = ({
    initTheme,
}: {
    initTheme: ThemeState["theme"];
}) => {
    const theme = useTheme((state) => state.theme);
    const changeTheme = useTheme((state) => state.changeTheme);
    const themeMap = {
        blue: themeBlueStyles.themeVariables,
        green: themeGreenStyles.themeVariables,
        gray: themeGrayStyles.themeVariables,
    };

    useEffect(() => {
        changeTheme(initTheme);
        console.log(initTheme);
    }, []);

    useEffect(() => {
        const themeClassName = themeMap[theme];
        document.documentElement.classList.add(themeClassName);
        return () => {
            document.documentElement.classList.remove(themeClassName);
        };
    }, [theme]);
    return (
        <div style={{ height: "100%", width: "100%" }}>
            <Outlet></Outlet>
        </div>
    );
};

const BAuthRoute = () => {
    const isLogin = useStore((state) => state.isLogin);
    const login = useStore((state) => state.login);
    const updateUserInfo = useStore((state) => state.updateUserInfo);
    const updateUserMenuPermission = useStore(
        (state) => state.updateUserMenuPermission,
    );
    const logout = useStore((state) => state.logout);

    const userInfoRequest = useRequest(
        () => {
            return Promise.all([
                bSideRequests.getUserInfo(),
                bSideRequests.getUserMenuList(),
            ]);
        },
        {
            ready: sessionStorage.getItem("jwt") !== null,
            async onSuccess(res) {
                const [useInfoRes, useMenuRes] = res;
                login();
                const telephone = await decryptStr(
                    useInfoRes?.data?.data?.phoneNumber,
                );
                const userName = await decryptStr(
                    useInfoRes?.data?.data?.userName,
                );
                const decObj = {
                    telephone,
                    userName,
                };
                const newObject = Object.assign(
                    {},
                    useInfoRes?.data?.data,
                    decObj,
                );
                updateUserInfo(newObject);
                updateUserMenuPermission(useMenuRes?.data?.data || []);
            },
            onError() {
                logout();
                sessionStorage.clear();
            },
        },
    );
    if (userInfoRequest.loading) {
        return (
            <div
                style={{
                    paddingTop: "30vh",
                    textAlign: "center",
                }}
            >
                <DotLoading></DotLoading>
            </div>
        );
    }
    if (isLogin) {
        return <Outlet></Outlet>;
    }
    return <Navigate to="/b/login" replace></Navigate>;
};
const BpersonalinfoWithTitle = () => {
    useEffect(() => {
        document.title = "个人信息";
    }, []);
    return <Bpersonalinfo />;
};

const BchangepasswordWithTitle = () => {
    useEffect(() => {
        document.title = "修改密码";
    }, []);
    return <Bchangepassword />;
};

const BLoginWithTitle = () => {
    useEffect(() => {
        document.title = "稻香汤原溯源平台";
    }, []);
    return <BLogin />;
};
export default createBrowserRouter(
    [
        {
            path: "/",
            element: <LastLocationProvider></LastLocationProvider>,
            children: [
                {
                    index: true,
                    element: <Navigate to="b" replace></Navigate>,
                },
                {
                    path: "b",
                    element: (
                        <ThemeVariableInject initTheme="blue"></ThemeVariableInject>
                    ),
                    children: [
                        {
                            index: true,
                            element: <Navigate to="home" replace></Navigate>,
                        },
                        {
                            path: "home",
                            element: <BAuthRoute></BAuthRoute>,
                            handle: {
                                canBack: true,
                            },
                            children: [
                                {
                                    index: true,
                                    element: <BHome></BHome>,
                                },
                                bProductiveProcessRoute,
                                bPackRoute,
                                bInboundRoute,
                                bOutboundRoute,
                                bSelfTransportationRoute,
                                bEntrustedTransportationRoute,
                                bPurchaseRoute,
                                bInboundManagement,
                            ],
                        },
                        {
                            path: "personalinfo",
                            element: (
                                <BpersonalinfoWithTitle></BpersonalinfoWithTitle>
                            ),
                        },
                        {
                            path: "changepassword",
                            element: (
                                <BchangepasswordWithTitle></BchangepasswordWithTitle>
                            ),
                        },
                        {
                            path: "changephone",
                            element: <Bchangephone></Bchangephone>,
                        },
                        {
                            path: "editpassword",
                            element: <Beditpassword></Beditpassword>,
                        },
                        {
                            path: "code",
                            element: <Bcode></Bcode>,
                        },
                        {
                            path: "login",
                            element: <BLoginWithTitle></BLoginWithTitle>,
                        },
                    ],
                },
                {
                    path: "c",
                    element: (
                        <ThemeVariableInject initTheme="blue"></ThemeVariableInject>
                    ),
                    children: [
                        {
                            index: true,
                            element: <Navigate to="trace" replace></Navigate>,
                        },

                        {
                            // path: "tracehome",
                            // element: (
                            //   <CacheRouteContainer
                            //     shouldCache={(location: any) => {
                            //       return location.pathname === "/c/tracehome";
                            //     }}
                            //   ></CacheRouteContainer>
                            // ),
                            path: "trace",
                            element: (
                                <CacheRouteContainer
                                    shouldCache={(location: any) => {
                                        return location.pathname === "/c/trace";
                                    }}
                                ></CacheRouteContainer>
                            ),
                            children: [
                                {
                                    index: true,
                                    element: <CTrace></CTrace>,
                                },
                                // {
                                //   path:'tracehome',
                                //   element: <CHome></CHome>,
                                // },
                                {
                                    path: "scan-log/:traceCodeId",
                                    element: <CScanLog></CScanLog>,
                                },
                                {
                                    path: "certificate/:transactionId",
                                    element: <CCertificate></CCertificate>,
                                },
                                {
                                    path: "error",
                                    element: (
                                        <CTraceErrorResult></CTraceErrorResult>
                                    ),
                                },
                            ],
                        },
                    ],
                },
                {
                    path: "*",
                    element: (
                        <div
                            style={{
                                paddingTop: "30%",
                                margin: "0 auto",
                                fontSize: "2em",
                                display: "flex",
                                justifyContent: "center",
                            }}
                        >
                            404 Page Not Found
                        </div>
                    ),
                },
            ],
        },
    ],
    // { basename: "/web" },
);

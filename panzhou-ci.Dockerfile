FROM artifactory.dep.devops.cmit.cloud:20101/tools/node:16.15.0	AS builder
ARG PROJECT_PATH
WORKDIR /app
# COPY ./${PROJECT_PATH}/package-lock.json ./
COPY ./${PROJECT_PATH}/package.json ./
RUN npm config set registry https://artifactory.dep.devops.cmit.cloud:20101/artifactory/api/npm/npm/
RUN npm config set sass_binary_site=https://artifactory.dep.devops.cmit.cloud:20101/artifactory/npm-remote-nodesass/
RUN npm config set disturl=https://artifactory.dep.devops.cmit.cloud:20101/artifactory/npm/node/
RUN npm install
COPY ./${PROJECT_PATH}/. .
RUN npm run build

FROM artifactory.dep.devops.cmit.cloud:20101/tools/base-images/nginx:alpine AS final
WORKDIR /app
COPY --from=builder /app/dist/ /usr/share/nginx/html
COPY --from=builder /app/nginx/nginx.conf /etc/nginx/nginx.conf
RUN mkdir -p ./ssl
# COPY --from=builder /app/ssl/test/ ./ssl/
EXPOSE 80
EXPOSE 443
ENV APISERVER=http://192.168.131.197:9022
# 复制启动脚本
COPY --from=builder /app/docker-init.sh .
# 设置可执行权限
RUN chmod +x ./docker-init.sh
# 进入容器启动
CMD ["/bin/sh", "./docker-init.sh"]
import React, { useState, useCallback, useEffect } from "react";
import {
    Button,
    Input,
    TextArea as AntdTextArea,
    Picker,
    Space,
    Modal,
    ImageUploader,
} from "antd-mobile";
import { AddOutline } from "antd-mobile-icons";
import classNames from "classnames";

import styles from "./index.module.less";

interface TextAreaViewProps {
    content: string;
    collapseSpaceWrap?: boolean
}

export default function TextAreaView(props: TextAreaViewProps) {
    const { content, collapseSpaceWrap=true, ...restProps } = props;
    let contentRet = content
    if(collapseSpaceWrap){
        contentRet = content.replace(/\n+/g, "\n").replace(/ +/g, " ")
    }
    return (
        <div style={{wordBreak:"break-all", whiteSpace:"pre-wrap"}}>{contentRet}</div>
    );
}

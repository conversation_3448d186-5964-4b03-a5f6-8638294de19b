import React, { useState, useRef, useEffect, useImperativeHandle } from "react";
import { Image, ImageViewer, Modal } from "antd-mobile";
import { PlayOutline } from "antd-mobile-icons";

import styles from "./index.module.less";
import { current } from "immer";

interface IPreviewVideoProps {
    src: string;
    preview?: boolean;
    className?: string;
    autoPlay?: boolean;
}
function PreviewVideo(props: IPreviewVideoProps, pref: any) {
    const { src, preview = true } = props;
    const videoRef = useRef<HTMLVideoElement>(null);
    // const pRef = useRef(null)
    const [poster, setPoster] = useState<string>();
    let progress = useRef(0);
    console.log("props", props);
    useImperativeHandle(pref, () => {
        return {
            getProgress() {
                // 获取进度
                return progress.current;
            },
            stopVideo() {
                if (videoRef.current) {
                    videoRef.current.pause();
                }
            },
        };
    });
    useEffect(() => {
        const video = videoRef.current!;
        const canvas = document.createElement("canvas");
        // const ctx = canvas.getContext("2d")!;
        function onLoadedmetadata() {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            video.currentTime = 0;
            video.play().then((res) => {
                video.pause();
                // video.play();
            });
            video.addEventListener("timeupdate", onTimeupdate);
        }
        function onTimeupdate() {
            // function () {
            progress.current = video.currentTime;
            // }
            // video.removeEventListener("loadedmetadata", onLoadedmetadata);
            // video.removeEventListener("timeupdate", onTimeupdate);
            // ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
            // canvas.toBlob((coverBlob) => {
            //     coverBlob && setPoster(URL.createObjectURL(coverBlob));
            // });
        }
        video.addEventListener("loadedmetadata", onLoadedmetadata);
        // video.addEventListener("timeupdate", onTimeupdate);
        return () => {
            video.removeEventListener("loadedmetadata", onLoadedmetadata);
            video.removeEventListener("timeupdate", onTimeupdate);
        };
    }, []);

    return (
        <div
            style={preview ? { width: "100%" } : undefined}
            className={styles.PreviewVideo + " " + props.className}
            onClick={() => {
                if (preview) {
                    const { close } = Modal.show({
                        className: styles.videoPreviewModal,
                        content: (
                            <div
                                onClick={() => {
                                    close();
                                }}
                                className="videoContainer"
                            ></div>
                        ),
                        closeOnMaskClick: true,
                    });
                }
            }}
        >
            {preview ? (
                <>
                    <video
                        ref={videoRef}
                        crossOrigin="anonymous"
                        src={src}
                        playsInline
                        autoPlay={true}
                    ></video>
                    <div className={styles.playBtnModal}>
                        <div className={styles.playBtnContainer}>
                            <PlayOutline
                                style={{
                                    fontSize: 32,
                                }}
                            />
                        </div>
                    </div>
                </>
            ) : (
                <video
                    style={{ width: "100%" }}
                    ref={videoRef}
                    crossOrigin="anonymous"
                    controls
                    src={src}
                    playsInline
                    autoPlay={true}
                ></video>
            )}
        </div>
    );
}

export default PreviewVideo;

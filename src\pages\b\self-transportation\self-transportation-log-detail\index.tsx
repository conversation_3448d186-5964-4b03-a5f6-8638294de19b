import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
    Button,
    Input,
    TextArea,
    Picker,
    Space,
    Toast,
    DotLoading,
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
    UpOutline,
    DownOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import { useRequest, useSetState } from "ahooks";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";
import CollapseTable from "@/components/collapse-table";

import iconScanQrCode from "@/assets/imgs/scan-qrcode.png";

import { showConfirmModal } from "@/utils";
import { bSideRequests } from "@/services";
import {
    TRANSPORTATION_STATE_CONSTANTS,
    TRANSPORTATION_TYPE_CONSTANTS,
} from "@/config";

import styles from "./index.module.less";

export default () => {
    const navigate = useNavigate();
    const { id } = useParams();

    const [unloadForm] = Form.useForm();

    const getTransportationDetailRequest = useRequest(
        () => {
            if (!id) {
                throw new Error("缺少id");
            }
            return bSideRequests.getTransportationDetail(id);
        },
        {
            refreshDeps: [id],
        },
    );
    const transportationDetail: Partial<bSideRequests.GetTransportationDetailRetData> =
        getTransportationDetailRequest.data?.data?.data || {};

    const deprecateTransportationRequest = useRequest(
        () => {
            if (!id) {
                throw new Error("缺少id");
            }
            return bSideRequests.deprecateTransportation(id);
        },
        {
            manual: true,
            onSuccess() {
                Toast.show({
                    icon: "success",
                    content: "作废成功",
                });
                unloadForm.resetFields();
                getTransportationDetailRequest.refresh();
            },
        },
    );

    const transportationUnloadReportRequest = useRequest(
        bSideRequests.transportationUnloadReport,
        {
            manual: true,
            onSuccess() {
                Toast.show({
                    icon: "success",
                    content: "上报卸货成功",
                });
                getTransportationDetailRequest.refresh();
            },
        },
    );

    const renderUnloadLocationItem = () => {
        if (
            transportationDetail.state !== undefined &&
            TRANSPORTATION_STATE_CONSTANTS.TRANSPORTATION_STATE_VK[
                transportationDetail.state
            ] === "SHIPPING"
        ) {
            return (
                <List.Item
                    extra={
                        <Form.Item
                            className="inboundInfoForm__unloadLocation"
                            name="unloadLocation"
                            rules={[
                                {
                                    required: true,
                                    message: "请输入卸货地点",
                                },
                                {
                                    max: 50,
                                    message: "不能超过50位",
                                },
                            ]}
                        >
                            <Input
                                style={{
                                    "--text-align": "right",
                                }}
                                placeholder="点击输入卸货地点"
                            ></Input>
                        </Form.Item>
                    }
                >
                    <div className="inboundInfoForm__requiredLabel">
                        卸货地点
                        <span className="inboundInfoForm__required">*</span>
                    </div>
                </List.Item>
            );
        } else {
            return (
                <List.Item
                    extra={transportationDetail?.unloadingLocation || "-"}
                >
                    卸货地点
                </List.Item>
            );
        }
    };

    if (getTransportationDetailRequest.loading) {
        return (
            <div
                style={{
                    paddingTop: "50px",
                    textAlign: "center",
                    color: "var(--adm-color-weak)",
                }}
            >
                <div style={{ fontSize: 24, marginBottom: 24 }}>
                    <DotLoading />
                </div>
                正在加载数据
            </div>
        );
    }

    return (
        <div className={`${styles.inboundLogDetail}`}>
            <Form form={unloadForm} layout="horizontal">
                <List>
                    <List.Item extra={transportationDetail?.transNumber || "-"}>
                        运输单号
                    </List.Item>
                    <List.Item
                        extra={transportationDetail?.loadingLocation || "-"}
                    >
                        装货地点
                    </List.Item>
                    <List.Item
                        extra={
                            transportationDetail?.transportationType
                                ? TRANSPORTATION_TYPE_CONSTANTS
                                      .TRANSPORTATION_TYPE_MAP_BY_VALUE[
                                      transportationDetail.transportationType
                                  ].name
                                : "-"
                        }
                    >
                        运输类型
                    </List.Item>
                    {renderUnloadLocationItem()}
                </List>
                <div className="boxCodeList">
                    <CollapseTable
                        columns={[
                            {
                                title:
                                    "箱码" +
                                    (transportationDetail?.boxTo?.length &&
                                    transportationDetail.boxTo.length > 0
                                        ? `（共${transportationDetail.boxTo.length}个）`
                                        : ""),
                                dataIndex: "boxCode",
                                width: "50%",
                            },
                            {
                                title: "产品名称",
                                dataIndex: "productName",
                                width: "40%",
                            },
                            {
                                width: "10%",
                            },
                        ]}
                        dataSource={transportationDetail?.boxTo || []}
                    ></CollapseTable>
                </div>

                {transportationDetail.state !== undefined &&
                    TRANSPORTATION_STATE_CONSTANTS.TRANSPORTATION_STATE_VK[
                        transportationDetail.state
                    ] === "SHIPPING" && (
                        <Space
                            direction="vertical"
                            style={{
                                width: "100%",
                                marginTop: 40,
                                "--gap": "12px",
                            }}
                        >
                            <Button
                                loading={
                                    transportationUnloadReportRequest.loading
                                }
                                onClick={async () => {
                                    try {
                                        const values =
                                            await unloadForm.validateFields();
                                        console.log(values, "values");
                                        if (!id) {
                                            throw new Error("缺少id");
                                        }
                                        showConfirmModal({
                                            title: "确认上报",
                                            onConfirm() {
                                                transportationUnloadReportRequest.run(
                                                    {
                                                        id: id,
                                                        unloadLocation:
                                                            values.unloadLocation,
                                                    },
                                                );
                                            },
                                        });
                                    } catch (failInfo: any) {
                                        const errorMsg =
                                            failInfo?.errorFields?.[0]
                                                ?.errors?.[0];
                                        Toast.show(errorMsg);
                                    }
                                }}
                                type="submit"
                                color="primary"
                                shape="rounded"
                                block
                            >
                                上报卸货
                            </Button>
                            <Button
                                loading={deprecateTransportationRequest.loading}
                                onClick={() => {
                                    showConfirmModal({
                                        title: "确认作废",
                                        content: "确认作废吗",
                                        confirmBtnProps: {
                                            color: "danger",
                                        },
                                        onConfirm() {
                                            deprecateTransportationRequest.run();
                                        },
                                    });
                                }}
                                color="primary"
                                fill="outline"
                                shape="rounded"
                                block
                            >
                                作废
                            </Button>
                        </Space>
                    )}
            </Form>
        </div>
    );
};

import { Form as AntdForm } from "antd-mobile";
import { LeftOutline } from "antd-mobile-icons";
import { useNavigate } from "react-router-dom";

import styles from "./index.module.less";

import type { FormProps } from "antd-mobile";

interface IFormProps extends FormProps {}
/**
 * docs: https://mobile.ant.design/zh/components/form
 */
const Form = (props: IFormProps) => {
    return (
        <div className={styles.form}>
            <AntdForm {...props}></AntdForm>
        </div>
    );
};
Form.Item = AntdForm.Item;
Form.useForm = AntdForm.useForm;
Form.Subscribe = AntdForm.Subscribe;
Form.useWatch = AntdForm.useWatch;

export default Form;

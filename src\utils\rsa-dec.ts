import request from "@/services";
import {
  Toast
} from "antd-mobile";
import JSEncrypt from "jsencrypt";

async function rsaDecrypte(encryptStr: string) {
  try {
    const decrypted = new JSEncrypt();
    const pKRet = await request({
      method: 'get',
      url: '/sys-config/getPubKey',
      data: {}
    });
    decrypted.setPrivateKey(pKRet?.data?.data || '');
    const decryptedWord = decrypted.decrypt(encryptStr);
    return decryptedWord || '';
  } catch (err) {
    Toast.show({
      content: "请求解密失败",
  });
    console.log(err);
    return Promise.reject();
  }
}

export default rsaDecrypte;

import React, { useState } from "react";
import { Ellipsis } from "antd-mobile";
import { UpOutline, DownOutline } from "antd-mobile-icons";
import { useNavigate } from "react-router-dom";

import styles from "./index.module.less";

import type { ListProps } from "antd-mobile";

interface ICollapseTableProps {
    dataSource: any[];
    columns: any[];
    rowKey?: (record: any) => string;
}
const CollapseTable = (props: ICollapseTableProps) => {
    const { dataSource, columns, rowKey } = props;
    const [tableVisible, setTableVisible] = useState(true);

    const colgroup = (
        <colgroup>
            {columns.map((item, index) => {
                const { width } = item;
                return <col key={index} style={{ width: width }} />;
            })}
        </colgroup>
    );
    return (
        <div className={styles.collapseTable}>
            <table>
                {colgroup}
                <thead>
                    <tr>
                        {columns.map((item, index) => {
                            const { title, thStyle } = item;
                            if (index === columns.length - 1) {
                                return (
                                    <th key={index} style={thStyle}>
                                        <div
                                            className="collapseTable__toggleIcon"
                                            onClick={() => {
                                                setTableVisible(!tableVisible);
                                            }}
                                        >
                                            {tableVisible ? (
                                                <UpOutline />
                                            ) : (
                                                <DownOutline />
                                            )}
                                        </div>
                                    </th>
                                );
                            }
                            return <th key={index}>{title}</th>;
                        })}
                    </tr>
                </thead>
            </table>
            <div
                style={
                    tableVisible
                        ? undefined
                        : {
                              height: 0,
                              overflow: "hidden",
                          }
                }
            >
                <table className="scanBoxRetList">
                    {colgroup}
                    <tbody>
                        {dataSource.map((dataItem, rowIndex) => {
                            let trKey: React.Key = rowIndex;
                            if (rowKey) {
                                trKey = rowKey(dataItem);
                            } else if (dataItem?.id) {
                                trKey = dataItem.id;
                            } else {
                                trKey = rowIndex;
                            }
                            return (
                                <tr key={trKey}>
                                    {columns.map((columnItem, columnIndex) => {
                                        const {
                                            render,
                                            dataIndex,
                                            style,
                                            ellipsis = true,
                                        } = columnItem;
                                        const value = render
                                            ? render(
                                                  dataItem[dataIndex],
                                                  dataItem,
                                                  rowIndex,
                                              )
                                            : dataItem[dataIndex];
                                        return (
                                            <td
                                                key={`${trKey}-${columnIndex}`}
                                                style={style}
                                            >
                                                {ellipsis &&
                                                typeof value === "string" ? (
                                                    <Ellipsis
                                                        style={{
                                                            wordBreak:
                                                                "break-word",
                                                        }}
                                                        content={value}
                                                    ></Ellipsis>
                                                ) : (
                                                    value
                                                )}
                                            </td>
                                        );
                                    })}
                                </tr>
                            );
                        })}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

export default CollapseTable;

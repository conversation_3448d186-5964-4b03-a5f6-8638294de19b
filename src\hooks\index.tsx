import { useState, useCallback, useEffect, useRef } from "react";
import {
    createBrowser<PERSON>outer,
    RouterProvider,
    Navigate,
    Outlet,
    useOutlet,
    useOutletContext,
    useLocation,
    useNavigate,
    Link,
    useMatches,
    useNavigationType,
    useBlocker,
} from "react-router-dom";
import {
    Button,
    Input,
    TextArea,
    Picker,
    Space,
    Mask,
    Toast,
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
    UpOutline,
    DownOutline,
    LeftOutline,
} from "antd-mobile-icons";
import useUrlState from "@ahooksjs/use-url-state";

import QRCodeScan from "@/components/qrcode-scan";

import { showConfirmModal } from "@/utils";

import type {
    unstable_Blocker as Blocker,
    unstable_BlockerFunction as BlockerFunction,
} from "react-router-dom";

import styles from "./index.module.less";

import useLastLocation from "./use-last-location.tsx";

export { useLastLocation };

interface IUseConfirmBlockProps {
    shouldBlock?: boolean | BlockerFunction;
    content?: string;
}
export const useConfirmBlock = (props?: IUseConfirmBlockProps) => {
    const { shouldBlock = true, content } = props || {};
    const blocker = useBlocker(shouldBlock);
    function onConfirm() {
        blocker.proceed?.();
    }
    function onCancel() {
        blocker.reset?.();
    }
    useEffect(() => {
        if (blocker.state === "blocked") {
            showConfirmModal({
                title: "确认离开",
                content: content,
                onConfirm,
                onCancel,
            });
        }
    }, [blocker]);
    return blocker;
};

type ScanFinishCallback = (qrCodeRet: string, continueScan: () => void) => void;
export const useScanQRCode = () => {
    const errStrRef = useRef<any>(null);
    const resolveRef = useRef<any>(null);
    const rejectRef = useRef<any>(null);
    const onScannedCallbackRef = useRef<ScanFinishCallback | null>(null);
    const [scanQrCodeVisible, setScanQrCodeVisible] = useState(false);
    const [isScan, setIsScan] = useState(false);

    /* 返回关闭扫描测试 */
    // const navigate = useNavigate();
    // const [urlState,setUrlState] = useUrlState({
    //     scanQrCodeVisible: "close"
    // },{
    // })
    // const scanQrCodeVisible = urlState.scanQrCodeVisible === "open" ? true : false
    // const setScanQrCodeVisible = (visible:boolean)=>{
    //     if(visible){
    //         setUrlState({
    //             scanQrCodeVisible: "open"
    //         })
    //     }else{
    //         navigate(-1)
    //     }
    // }

    const startScanQrCode = async (onFinish: ScanFinishCallback) => {
        console.log("startScanQrCode 被调用");
        onScannedCallbackRef.current = onFinish;
        const errStr = errStrRef.current;
        console.log("当前错误状态:", errStr);
        if (errStr) {
            console.log("有错误，显示错误信息:", errStr);
            Toast.show({
                icon: "fail",
                content: errStr,
            });
            return Promise.reject(errStr);
        }

        console.log("设置扫码可见状态");
        setScanQrCodeVisible(true);
        setIsScan(true);

        readOneQrCode();
    };

    const readOneQrCode = () => {
        resolveRef.current = null;
        rejectRef.current = null;

        return new Promise<string>((resolve, reject) => {
            resolveRef.current = resolve;
            rejectRef.current = reject;
        }).then((qrCodeRet) => {
            const onFinish = onScannedCallbackRef.current;
            setIsScan(false);
            onFinish?.(qrCodeRet, () => {
                // 继续扫描
                setIsScan(true);
                readOneQrCode();
            });
        });
    };

    const closeScanQrCode = () => {
        rejectRef.current?.();
        resolveRef.current = null;
        rejectRef.current = null;
        onScannedCallbackRef.current = null;
        errStrRef.current = null; // 清除错误信息，确保下次可以正常启动
        setScanQrCodeVisible(false);
        setIsScan(false);
    };

    const qrCodeScanElement = (
        <Mask
            className={styles.qrCodeMask}
            visible={scanQrCodeVisible}
            color="black"
        >
            <QRCodeScan
                shouldScan={scanQrCodeVisible && isScan}
                onScanned={(ret) => {
                    resolveRef.current?.(ret);
                    resolveRef.current = null;
                    rejectRef.current = null;
                }}
                onStartError={(errStr) => {
                    errStrRef.current = errStr;
                    Toast.show({
                        icon: "fail",
                        content: errStr,
                    });
                    closeScanQrCode();
                }}
            ></QRCodeScan>
            <div
                className="backBtn"
                onClick={() => {
                    closeScanQrCode();
                }}
            >
                <LeftOutline
                    style={{
                        color: "black",
                        fontSize: 16,
                    }}
                />
            </div>
        </Mask>
    );

    return { qrCodeScanElement, startScanQrCode, closeScanQrCode };
};

/**
 * @expiremental
 */
export const useBackRoute = () => {
    const matches = useMatches();
    const location = useLocation();
    const navigate = useNavigate();
    const navigationType = useNavigationType();
    const lastLocation = useLastLocation();

    // useEffect(()=>{
    //     console.log('matches','matches')
    // },[])

    const handleBack = () => {
        // @ts-ignore
        const canBackRoute = matches.filter((item) => item?.handle?.canBack);
        const prevPath = canBackRoute[canBackRoute.length - 2]?.pathname;
        const hasBackHistory =
            lastLocation &&
            lastLocation.key !== "default" &&
            navigationType !== "REPLACE";
        if (hasBackHistory || !prevPath) {
            navigate(-1);
        } else {
            navigate(prevPath, { replace: true });
        }
    };

    return handleBack;
};

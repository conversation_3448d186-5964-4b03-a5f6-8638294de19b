// import React from 'react'
import './index.less'
import { NavBar, Toast, Form, Input, Image, Button } from 'antd-mobile'
import okBtn from '@/assets/imgs/bimgs/btn.png'
import { useNavigate } from "react-router-dom";
import { bSideRequests } from "@/services";
import request from "@/services";
import { rsaEncrypt } from "@/utils";
import { useRef } from 'react';
export default () => {
  const navigate = useNavigate();
  let formRef = useRef<any>(null)

  const formValChange = () =>{
    let formInfo =  formRef.current.getFieldsValue()
      //  console.log('触发',formInfo)
     if(formRef.current&&formInfo.newPhone&&formInfo.oldPhone&&formInfo.repeatPhone){
      formRef.current.validateFields()
     }
   }
  const back = () => {
    navigate("/b/personalinfo");
  }
  // 修改手机号
  const ok = async (info: any) => {
    // console.log(info)
    const pKRet = await request({
      method: "get",
      url: "/sys-config/getPublicKey",
      data: {},
    });
    const pK = pKRet?.data?.data || "";
    let parms = {
      oldPhone: await rsaEncrypt(
        info.oldPhone,
        pK,
      ), // 旧
      newPhone: await rsaEncrypt(
        info.newPhone,
        pK,
      ),// 新
    }
    bSideRequests.modifyPhone(parms).then(res => {
      if (res.data.code === 200) {
        Toast.show(res.data.data)
        sessionStorage.clear();
        localStorage.clear();
        navigate("/b/login");
      }

    })
  }
  return (
    <div className='info_box'>
      <div className='back_img'>
        <NavBar onBack={back}>修改联系方式</NavBar>
      </div>
      <div className='info_div wrap_input--style' >
        <Form
          layout='horizontal'
          onFinish={ok}
          ref={formRef} onValuesChange={formValChange}
        >
          <div className='flex_wrap'>
            <div className='text'>旧联系方式</div>
            <Form.Item name='oldPhone'

              rules={[
                {
                  required: true,
                  message: '旧联系方式不能为空'
                }, {
                  pattern: /^[1][3,4,5,6.7,8,9][0-9]{9}$/,
                  message: "请输入正确的手机号",
                },]}>
              <Input clearable className="formItem__input" type="tel" />
            </Form.Item>
          </div>
          <div className='flex_wrap'>
            <div className='text'>新联系方式</div>
            <Form.Item name='newPhone' rules={[
              {
                required: true,
                message: '新联系方式不能为空'
              },
              {
                pattern: /^[1][3,4,5,6.7,8,9][0-9]{9}$/,
                message: "请输入正确的手机号",
              },
            ]}>
              <Input clearable type='text' />
            </Form.Item>
          </div>
          <div className='flex_wrap'>
            <div className='text'>确认联系方式</div>
            <Form.Item name='repeatPhone' rules={[
              {
                required: true,
                message: '确认联系方式不能为空'
              },
              ({ getFieldValue }) => ({
                validator(rule, value) {
                  if (!value || getFieldValue('newPhone') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject('两次手机号输入不一致！');
                }
              })
            ]}>
              <Input clearable type='text' />
            </Form.Item>
          </div>
          <div className='okbtn'>
            <Button color='primary' style={{ width: '100%', height: '100%' }} fill='none' type="submit">
              确认
            </Button>
          </div>

        </Form>
      </div>
    </div>
  )
}

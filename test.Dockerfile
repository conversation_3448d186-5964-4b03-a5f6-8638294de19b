FROM node:18-alpine AS builder
WORKDIR /app
COPY package.json package-lock.json ./
RUN npm config set registry "https://registry.npmmirror.com" && npm install --legacy-peer-deps
COPY . .
RUN npm run build

FROM nginx:alpine AS final
WORKDIR /app
COPY --from=builder /app/dist/ /usr/share/nginx/html
RUN mkdir -p ./ssl
# 转为通过环境变量进行设置
# COPY --from=builder /app/ssl/test/ ./ssl/
EXPOSE 80
EXPOSE 443
ENV APISERVER=http://10.0.42.60:9099
ENV ssl_certificate="LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURWakNDQWo0Q0NRRHkxcVBRUENzeW9qQU5CZ2txaGtpRzl3MEJBUVVGQURCdE1Rc3dDUVlEVlFRR0V3SkQKVGpFS01BZ0dBMVVFQ0F3Qk1URUtNQWdHQTFVRUJ3d0JNVEVLTUFnR0ExVUVDZ3dCTVRFS01BZ0dBMVVFQ3d3QgpNVEVjTUJvR0ExVUVBd3dUTVRreUxqRTJPQzR4TXpFdU5ETTZOREF3TURFUU1BNEdDU3FHU0liM0RRRUpBUllCCk1UQWVGdzB5TXpFd01UY3dNalUzTVRoYUZ3MHlNekV4TVRZd01qVTNNVGhhTUcweEN6QUpCZ05WQkFZVEFrTk8KTVFvd0NBWURWUVFJREFFeE1Rb3dDQVlEVlFRSERBRXhNUW93Q0FZRFZRUUtEQUV4TVFvd0NBWURWUVFMREFFeApNUnd3R2dZRFZRUUREQk14T1RJdU1UWTRMakV6TVM0ME16bzBNREF3TVJBd0RnWUpLb1pJaHZjTkFRa0JGZ0V4Ck1JSUJJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBdVpNeWp3MWV0blVsQTUrK1VFREMKSlJJQXdIbEpjMHVGZmJRd2dtT3FlUXJ0TTA4QW1rZHlaT21TYWs2ekpmaEY4eFNoZ2VHUjk5OEdIMkk3UEJxVwpOeHliSkplSUZQL20yVWxHZmEycW84Q3hlWURGTHBETW9sWG41cG5EcVg5c2hZTmJtdmZuV2o5akIwVmxBWmJNClN1YnU2cmRwSnl2NjdjMkhlcVhYbXllTTVhNWw3UUhZNVl4WHBtMHJDem1Sck1iM2hjRUNBeFhGMGFLbnM5OXUKOHBwZXhZTnFmZjVIUDFMelZ6MjBYeVpXVkcyeHFqeVFuazc4ekUxVVVRY0kwaGtrZEtyZU9MakhHR00vUUJJMQpRYWJvSHJZWEFFU21OdGV1RDk3L0VvdDJFV3dsNGYyM2lzcEdNMlBBQkRyVkhTeStldE43bk9rRDNlT0lrWisyCmhRSURBUUFCTUEwR0NTcUdTSWIzRFFFQkJRVUFBNElCQVFCVVdpYkRCYU4wNThsUjR6bVlnaFBzN2dEZDZzSzgKcjB1Qm13STFRN0R1SVU2eXRYNVVadG8vUWh5c1JsUEhER2x4alljUUpoSW0vTHh1N3dWS1I2OUUxU2c5QjgyOAoxUmNVcjVUTm9od2lGQ2JOVjlaWC9WbHFIcFFteE1UclBqODhNODNDMk5naGtvUzllaVQ2WmhKQTRTQzBkVWpPCjRESHRJd2RPZ3BaSzBlTXExVCtXOEhjYkxacTNnemhSN2xQMHoxeDJVYVowdklNbXFJUjlWTVhZV25BQy8xT2oKSnhmYlB3QWJ2cDByQjlkSndxRkJuT3hUUU5oNTNPSXNic3VraGRHSUx3WkRuSW82MzlhMXFVTjhmUXY3SnVxOQpnNEZQL0RJdUNNWEViekg2T0swLzVEWU9BRGlNcHRDMU1KU09lZGg3MXFuWktoTjV0RHJhNFpTUgotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg=="
ENV ssl_certificate_key="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
# 复制启动脚本
COPY --from=builder /app/docker-init.sh .
# COPY ./docker-init.sh .
# 设置可执行权限
RUN chmod +x ./docker-init.sh
# 进入容器启动
CMD ["/bin/sh", "./docker-init.sh"]

# 构建
# docker build -t cm-ft-h5-test -f test.Dockerfile .
# --build-arg 设置构建过程中的环境变量

# 运行
# - ssl_certificate base64后的ssl证书 cat xxx | base64
# - ssl_certificate_key base64后的ssl私钥
# docker run -dp 4000:443 4001:80 -e APISERVER=http://192.168.131.197:9022 -e ssl_certificate=xx -e ssl_certificate_key=xx  cm-ft-h5-test


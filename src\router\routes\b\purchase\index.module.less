.layout {
    background:
        var(--page-bg-img) no-repeat center top/100%,
        var(--page-bg-color);
}
.Btn {
  height: 60px;
  margin-top: 25px;
  // border-radius: 10px;
  // padding: 13px 12px 15px 12px;
  font-size: 16px;
  line-height: 19px;
  letter-spacing: 0px;
  background-image: url('@/assets/imgs/bimgs/btn1.png');
  background-size: 104% 106%;
  background-position: center 3px;
  background-repeat: no-repeat;
  // background-color: transparent;
  // background: red;
  border: 0;

  span {
      color: white;
  }
}
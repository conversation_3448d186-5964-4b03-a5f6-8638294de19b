import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
    Button,
    Input,
    TextA<PERSON>,
    Picker,
    Space,
    DotLoading,
    Ellipsis
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
    UpOutline,
    DownOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import { useRequest, useSetState } from "ahooks";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";
import CollapseTable from "@/components/collapse-table";

import iconScanQrCode from "@/assets/imgs/scan-qrcode.png";

import { bSideRequests } from "@/services";
import { REPOSITORY_TYPE_CONSTANTS } from "@/config";

import styles from "./index.module.less";

export default () => {
    const navigate = useNavigate();
    const { id } = useParams();

    const getInboundDetailRequest = useRequest(
        () => {
            if (!id) {
                throw new Error("缺少id");
            }
            return bSideRequests.getInboundDetail(id);
        },
        {
            refreshDeps: [id],
        },
    );
    const inboundDetail = getInboundDetailRequest.data?.data?.data || {};

    if (getInboundDetailRequest.loading) {
        return (
            <div
                style={{
                    paddingTop: "50px",
                    textAlign: "center",
                    color: "var(--adm-color-weak)",
                }}
            >
                <div style={{ fontSize: 24, marginBottom: 24 }}>
                    <DotLoading />
                </div>
                正在加载数据
            </div>
        );
    }

    return (
        <div className={`${styles.inboundLogDetail}`}>
            <List>
                <List.Item extra={<Ellipsis content={inboundDetail?.warehouseNumber || "-"}></Ellipsis>}>
                    入库单号
                </List.Item>
                <List.Item extra={inboundDetail?.storehouse || "-"}>
                    仓库名称
                </List.Item>
                <List.Item extra={inboundDetail?.storehouseArea || "-"}>
                    仓储地点
                </List.Item>
                <List.Item extra={inboundDetail?.position || "-"}>
                    仓位
                </List.Item>
                <List.Item
                    extra={
                        ![undefined, null].includes(
                            inboundDetail?.storehouseType,
                        )
                            ? // @ts-ignore
                              REPOSITORY_TYPE_CONSTANTS
                                  .REPOSITORY_TYPE_MAP_BY_VALUE[
                                  inboundDetail.storehouseType
                              ].name
                            : "-"
                    }
                >
                    仓库类型
                </List.Item>
            </List>
            <div className="boxCodeList">
                <CollapseTable
                    columns={[
                        {
                            title:
                                "箱码" +
                                (inboundDetail?.boxTo?.length &&
                                inboundDetail.boxTo.length > 0
                                    ? `（共${inboundDetail.boxTo.length}个）`
                                    : ""),
                            dataIndex: "boxCode",
                            width: "50%",
                        },
                        {
                            title: "产品名称",
                            dataIndex: "productName",
                            width: "40%",
                        },
                        {
                            width: "10%",
                        },
                    ]}
                    dataSource={inboundDetail?.boxTo || []}
                ></CollapseTable>
            </div>
        </div>
    );
};

//out:false

.logListBox {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    .logList {
        margin: 10px;
        padding: 20px 16px;
        background: var(--page-block-color);
        border-radius: var(--border-radius);
        :global {
            .packLogList {
                margin-top: 25px;

                &__item {
                    &--title {
                        width: 280px;
                        white-space: nowrap; /* 禁止换行 */
                        overflow: hidden; /* 隐藏溢出内容 */
                        text-overflow: ellipsis; /* 显示省略号 */
                    }
                    &--info {
                        font-size: var(--text-sm);
                        color: var(--text-secondary);
                        // display: flex;
                        // gap: 20px;
                        &--text {
                            display: flex;
                            &--title {
                                width: 80px;
                            }
                            &--center {
                                width: 200px;
                                white-space: nowrap; /* 禁止换行 */
                                overflow: hidden; /* 隐藏溢出内容 */
                                text-overflow: ellipsis; /* 显示省略号 */
                            }
                        }
                    }
                }
            }
        }
    }

    .BtnBack {
        // border: 1px solid #000;
        box-shadow: inset 0 1px 2px rgba(107, 95, 95, 0.2);
        position: sticky;
        bottom: 0;
        background: #fff;

        .Btn {
            width: 300px;
            height: 60px;
            margin-left: 40px;
            // border-radius: 10px;
            // padding: 13px 12px 15px 12px;
            font-size: 16px;
            line-height: 19px;
            letter-spacing: 0px;
            background-image: url("@/assets/imgs/bimgs/btn1.png");
            background-size: 104% 106%;
            background-position: center 3px;
            background-repeat: no-repeat;
            // background-color: transparent;
            // background: red;
            border: 0;

            span {
                color: white;
            }
        }
    }
}

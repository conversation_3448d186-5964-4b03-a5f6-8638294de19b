.confirmModal {
    :global {
        .adm-modal-content {
            padding: 0;
        }
        .adm-modal-footer-empty {
            display: none;
        }
        .content {
            padding: 20px;
            padding-bottom: 10px;
            color: var(--text-secondary);
            text-align: center;
        }
        .actions {
            margin-top: 10px;
            width: 100%;
            display: flex;
            button {
                display: block;
                flex: 1;
                border-radius: 0;
                border-top: 1px solid #eee;
                height: 50px;
                &::before {
                    border-radius: 0;
                }
                & + button {
                    border-left: 1px solid #eee;
                }
            }
        }
        .adm-button:not(.adm-button-default).adm-button-fill-none {
            color: #ffaa00;
        }
    }
}

.ToastLoading {
    :global {
        .adm-toast-main {
            background: white;
            color: black;
        }
        .adm-toast-mask .adm-toast-main-icon .adm-toast-icon {
            margin-bottom: 19px;
        }
        .adm-spin-loading-svg > .adm-spin-loading-fill {
            stroke: var(--primary-color);
        }
    }
}

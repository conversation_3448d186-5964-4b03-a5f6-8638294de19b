import React from "react";

import styles from "./index.module.less";

interface ITraceBlockTitleProps {
    children: React.ReactNode;
}
function TraceBlockTitle(props: ITraceBlockTitleProps) {
    const { children } = props;
    return (
        <div
            className={styles.TraceBlockTitle}
            style={children != "企业基础信息" ? { width: "40%" } : {}}
        >
            {/* <div className={styles.diamond}></div> */}
            {children == "企业基础信息" ? (
                <div className={styles.title_text1}>{children}</div>
            ) : (
                <div
                    className={styles.title_text}
                    style={
                        children == "生产商"
                            ? {
                                  position: "absolute",
                                  left: "58px",
                              }
                            : {}
                    }
                >
                    {children}
                </div>
            )}

            {/* <div className={styles.diamond}></div> */}
        </div>
    );
}

export default TraceBlockTitle;

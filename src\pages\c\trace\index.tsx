import {
    useState,
    useEffect,
    useRef,
    useImper<PERSON><PERSON><PERSON><PERSON>,
    forwardRef,
} from "react";
import { Navigate, useNavigate, Link, useParams } from "react-router-dom";
import {
    Button,
    Input,
    TextArea,
    Picker,
    DotLoading,
    Tabs,
    Image,
    Ellipsis,
    ErrorBlock,
    FloatingBubble,
    Rate,
    Popup,
    Form,
    Toast,
    Selector,
    Space,
    NumberKeyboard,
    PasscodeInput,
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
    RightOutline,
    CheckCircleFill,
    MessageFill,
    CloseOutline,
} from "antd-mobile-icons";
import useUrlState from "@ahooksjs/use-url-state";
import { useRequest, useSetState } from "ahooks";
import dayjs from "dayjs";
import classNames from "classnames";
import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import PreviewImages from "@/components/preview-images";
import PreviewVideoOrigin from "@/components/preview-video";
import TextAreaView from "@/components/text-area-view";

import TraceBlock, { isTraceBlockEmpty } from "./components/trace-block";
import iconWarning from "@/assets/imgs/c/icon-warning.png";
import useStore, { useTheme } from "@/store";

import { cSideRequests } from "@/services";
import {
    TRANSPORTATION_STATE_CONSTANTS,
    TRANSPORTATION_TYPE_CONSTANTS,
} from "@/config";
import request from "@/services";
import styles from "./index.module.less";
import { decryptedUrl, isArrayArr } from "@/utils/index";
import jian from "@/assets/imgs/c/jian.png";
import one from "@/assets/imgs/c/1.png";
import tow from "@/assets/imgs/c/2.png";
import there from "@/assets/imgs/c/3.png";
import four from "@/assets/imgs/c/4.png";
import CSvideo from "../../../assets/imgs/video/videoa.mp4";
import {
    showToastLoading,
    signatureByEncryptedPrivateKey,
    showConfirmModal,
    requestDataSign,
    requestDataSignC,
} from "@/utils";
import type { ToastLoadingHandler } from "@/utils";

enum TraceTabsKey {
    trace = "trace",
    product = "product",
    inspection = "inspection",
    enterprise = "enterprise",
}
const PreviewVideo = forwardRef(PreviewVideoOrigin);
const Trace = () => {
    useEffect(() => {
        document.addEventListener(
            "click",
            () => {
                if (VideoRef.current) {
                    VideoRef.current.play();
                }
            },
            { once: true },
        );
    }, []);

    const navigate = useNavigate();

    const [urlState, setUrlState] = useUrlState<{
        traceCodeId?: string;
    }>();
    const [traceTabsActiveKey, setTraceTabsActiveKey] = useState<TraceTabsKey>(
        TraceTabsKey.trace,
    );
    const [promotionPicData, setPromotionPicData] = useState<any>();
    const [video, setVideo] = useState<any>();

    const [processDecImg, setProcessDecImg] = useState<any>([]);
    const [processDecVideo, setProcessDecVideo] = useState<any>([]);
    const [inspectionReportDec, setInspectionReportDec] = useState<any>();
    const [orgBasicQualification, setOrgBasicQualification] = useState<any>();
    const [orgBriefImg, setOrgBriefImg] = useState<any>();
    const [orgBriefVideo, setOrgBriefVideo] = useState<any>();

    const [pbVideo, setPbVideo] = useState<any>();
    const [pbImg, setPbImg] = useState<any>();
    const [pbApt, setPbApt] = useState<any>();
    //
    const [showHome, setShowHome] = useState(false);
    const [showInputPage, setShowInputPage] = useState(false);
    const [homeImg, setHomeImg] = useState<any>([]);
    const [videoSrc, setVideoSrc] = useState("");
    const [showVideo, setShowVideo] = useState(true);
    const [showAudio, setAudio] = useState(false);
    const [inputValue, setInputValue] = useState("");
    const [inputError, setInputError] = useState("");
    const [showKeyboard, setShowKeyboard] = useState(false);
    const [needInputPage, setNeedInputPage] = useState<boolean | null>(null); // 是否需要显示输入页面
    const [verifiedCode, setVerifiedCode] = useState<string>(""); // 存储验证成功的验证码
    const AudioRef = useRef<any>(null);
    const VideoRef = useRef<any>(null);
    const passcodeInputRef = useRef<any>(null);
    console.log("新增注释看提交");
    const [showRatingPopup, setShowRatingPopup] = useState(false);
    const [rating, setRating] = useState(0);
    const [showFeedbackForm, setShowFeedbackForm] = useState(false);
    const [selectedOptions, setSelectedOptions] = useState<Array<string>>([]);
    const [feedbackDescription, setFeedbackDescription] = useState("");
    const [otherDescription, setOtherDescription] = useState("");
    const [contactPhone, setContactPhone] = useState("");
    const [showConfirmDialog, setShowConfirmDialog] = useState(false);
    const [feedbackError, setFeedbackError] = useState("");
    const [productNameText, setProductNameText] = useState("");

    // 添加查询记录弹框状态
    const [showQueryRecords, setShowQueryRecords] = useState(false);

    const updateUserInfo = useStore((state) => state.updateUserInfo);
    const [form] = Form.useForm();
    // 用于存储定时器ID的ref
    const ratingTimerRef = useRef<NodeJS.Timeout | null>(null);
    // const userInfo = JSON.parse(sessionStorage.userInfo);
    const toastLoadingHandlerRef = useRef<ToastLoadingHandler | null>(null);

    // 意见反馈请求
    const productiveProcessEntryRequest = useRequest(
        (params: any) => {
            return cSideRequests.getFeekBackRequest(params);
        },
        {
            manual: true,
            onSuccess() {
                toastLoadingHandlerRef?.current?.close?.();
                setShowRatingPopup(false);
                Toast.show("反馈成功");
                // entryForm.resetFields();
            },
            onError(error) {
                toastLoadingHandlerRef?.current?.close?.();
                Toast.show(error?.response?.data?.message);
            },
        },
    );

    // 添加useEffect来监听selectedOptions变化，当选项不包含4时清空商品质量描述
    useEffect(() => {
        if (!selectedOptions.includes("4")) {
            form.setFieldValue("productQuality", undefined);
        }
    }, [selectedOptions, form]);

    // 添加useEffect来监听selectedOptions变化，当选项不包含5时清空其他建议描述
    useEffect(() => {
        if (!selectedOptions.includes("5")) {
            form.setFieldValue("otherSuggestion", undefined);
        }
    }, [selectedOptions, form]);

    // 添加useEffect来监听selectedOptions变化，当选项不包含4和5时清空联系电话
    useEffect(() => {
        if (!selectedOptions.includes("4") && !selectedOptions.includes("5")) {
            form.setFieldValue("phone", undefined);
        }
    }, [selectedOptions, form]);

    // 监听showInputPage变化，自动弹出键盘并聚焦
    useEffect(() => {
        if (showInputPage) {
            // 延迟一下确保页面渲染完成
            setTimeout(() => {
                setShowKeyboard(true);
                // 聚焦到第一个输入框
                if (passcodeInputRef.current) {
                    passcodeInputRef.current.focus();
                }
            }, 100);
        }
    }, [showInputPage]);

    // 检查是否需要显示输入页面的请求
    const checkInputPageRequest = useRequest(
        () => {
            if (!urlState.traceCodeId) {
                navigate("error?msg=找不到溯源码ID", {
                    replace: true,
                });
                return Promise.reject();
            }
            return cSideRequests.checkNeedInputPage(urlState.traceCodeId);
        },
        {
            onSuccess(res: any) {
                const needInput = res?.data?.data?.isEnabled === true;
                setProductNameText(res?.data?.data?.productName);
                setNeedInputPage(needInput);

                if (needInput) {
                    // 如果需要输入页面，先显示输入页面，不立即请求其他接口
                    setShowInputPage(true);
                } else {
                    // 如果不需要输入页面，直接请求其他四个接口
                    traceCodeDetailRequest.run();
                    traceCodeProductDetailRequest.run();
                    traceCodeInspectionDetailRequest.run();
                    traceCodeOrgDetailRequest.run();
                }
            },
            onError(ret: any) {
                const errMsg = ret?.response?.data?.message || "请求失败";
                navigate(`error?msg=${errMsg}`, {
                    replace: true,
                });
            },
        },
    );

    // 验证防伪码的请求
    const verifyCodeRequest = useRequest(
        (params: { id: string; code: string }) => {
            return cSideRequests.verifyCode(params);
        },
        {
            manual: true,
            onSuccess(res: any) {
                const isValid = res?.data?.data === true;
                if (isValid) {
                    // 验证成功，保存验证码，清除错误信息并进入主页面
                    setVerifiedCode(inputValue.trim());
                    setInputError("");
                    handleInputSuccess();
                    traceCodeDetailRequest.run(inputValue);
                    traceCodeProductDetailRequest.run(inputValue);
                    traceCodeInspectionDetailRequest.run(inputValue);
                    traceCodeOrgDetailRequest.run(inputValue);
                } else {
                    // 验证失败，显示错误信息，清空输入框让用户重新输入
                    setInputError(res.data.message);
                    setInputValue("");
                }
            },
            onError(ret: any) {
                const errMsg =
                    ret?.response?.data?.message || "验证失败，请重试";
                setInputError(errMsg);
                // 验证出错时也清空输入框
                setInputValue("");
            },
        },
    );

    const traceCodeDetailRequest = useRequest(
        (code?: string) => {
            if (!urlState.traceCodeId) {
                navigate("error?msg=找不到溯源码ID", {
                    replace: true,
                });
                return Promise.reject();
            }
            return cSideRequests.getTraceDataByTraceCodeId(
                urlState.traceCodeId,
                code,
            );
        },
        {
            manual: true, // 改为手动触发
            async onSuccess(res: any) {
                console.log("sssswwww===111---2", res);
                const result = res?.data?.data?.process;

                const videosrc = decryptedUrl(res.data.data.openScreenVideo);
                //  console.log('video**************************111111111111111',videosrc);
                videosrc
                    .then((result) => {
                        // 设置视频路径
                        setVideoSrc(result);
                    })
                    .catch((error) => {
                        // 处理错误
                        console.error(error);
                    });
                // decryptedUrl(res.data.data.openScreenVideo).then(r => {
                //   // 设置视频路径
                //   setVideoSrc(r)
                //   console.log('video**************************111111111111111',res.data.data.openScreenVideo);

                // })

                const list = [];
                const allocationImg = decryptedUrl(res.data.data.openScreenImg);
                // const itemArr = await Promise.all(isArrayArr(item?.processImg)?.map((val: string) => decryptedUrl(val)))
                // console.log('img**************************111111111111111', allocationImg);
                allocationImg
                    .then((result) => {
                        setHomeImg(result);
                    })
                    .catch((error) => {
                        // 处理错误
                        console.error(error);
                    });
                // list.push(allocationImg)
                // console.log('img2222222222222222222222222222222222222', list);

                const arrayDataProcessImg = await Promise.all(
                    isArrayArr(result)?.map(async (item: any) => {
                        const arr = [];
                        if (
                            Array.isArray(item?.processImg) &&
                            item?.processImg?.length > 0
                        ) {
                            const itemArr = await Promise.all(
                                isArrayArr(item?.processImg)?.map(
                                    (val: string) => decryptedUrl(val),
                                ),
                            );
                            console.log(itemArr);
                            arr.push(...itemArr);
                        }
                        return arr;
                    }),
                );
                console.log("pp0000");
                setProcessDecImg(arrayDataProcessImg);

                console.log("pp1111111");
                const arrayDataProcessVideo = await Promise.all(
                    isArrayArr(result)?.map((item: any) => {
                        console.log("item?.processVideo", item?.processVideo);
                        if (item?.processVideo)
                            return decryptedUrl(item?.processVideo);
                        return null;
                    }),
                );
                setProcessDecVideo(arrayDataProcessVideo);
                // console.log('aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa', arrayDataProcessVideo);
            },
            onError(ret: any) {
                const errMsg = ret?.response?.data?.message || "请求失败";
                const errCode = ret?.response?.data?.code;
                navigate(`error?msg=${errMsg}`, {
                    replace: true,
                });
            },
        },
    );

    // 组件卸载时清除定时器
    useEffect(() => {
        return () => {
            if (ratingTimerRef.current) {
                clearTimeout(ratingTimerRef.current);
                ratingTimerRef.current = null;
            }
        };
    }, []);

    const traceCodeDetail = traceCodeDetailRequest.data?.data?.data || {};
    // 查询记录数据
    const queryRecords = traceCodeDetail.traceRecordTo || [];
    const traceCodeProductDetailRequest = useRequest(
        (code?: string) => {
            if (!urlState.traceCodeId) {
                return Promise.reject();
            }
            return cSideRequests.getProductTraceDataByTraceCodeId(
                urlState.traceCodeId,
                code,
            );
        },
        {
            manual: true, // 改为手动触发
            async onSuccess(res: any) {
                console.log("ssss===", res);
                const result = res?.data?.data?.productBrief;
                if (result?.productVideo) {
                    const videos = await decryptedUrl(result?.productVideo);
                    setVideo(videos);
                }
                if (result?.productImg) {
                    const arrayData = await Promise.all(
                        isArrayArr(result?.productImg)?.map((item: any) => {
                            return decryptedUrl(item);
                        }),
                    );
                    // const image = await decryptedUrl(result?.productImg)
                    console.log(
                        "adscsdcffvfdvdavfvcfv122222313131312312332eedscsdcsdcsdc",
                        arrayData,
                    );

                    setPromotionPicData(arrayData);
                }
                const placebrief = res?.data?.data?.placeBrief;
                if (placebrief?.placeVideo) {
                    const pbvideos = await decryptedUrl(placebrief?.placeVideo);
                    setPbVideo(pbvideos);
                }
                const pbImgs = await Promise.all(
                    isArrayArr(placebrief?.placeImg)?.map((item: any) => {
                        return decryptedUrl(item);
                    }),
                );
                //const image = await decryptedUrl(result?.productImg)

                setPbImg(pbImgs);

                const placebasic = res?.data?.data?.placeBasic;
                const pbApts = await decryptedUrl(placebasic?.placeAptitude);
                setPbApt(pbApts);
            },
        },
    );
    const traceCodeProductDetail =
        traceCodeProductDetailRequest.data?.data?.data || {};

    const traceCodeInspectionDetailRequest = useRequest(
        (code?: string) => {
            if (!urlState.traceCodeId) {
                return Promise.reject();
            }
            return cSideRequests.getInspectionTraceDataByTraceCodeId(
                urlState.traceCodeId,
                code,
            );
        },
        {
            manual: true, // 改为手动触发
            async onSuccess(res: any) {
                const result = res?.data?.data;
                const arrayData = await Promise.all(
                    isArrayArr(result)?.map((item: any) => {
                        return decryptedUrl(item.inspectionReport);
                    }),
                );
                setInspectionReportDec(arrayData);
            },
        },
    );
    const traceCodeInspectionDetail =
        traceCodeInspectionDetailRequest.data?.data?.data || [];

    const traceCodeOrgDetailRequest = useRequest(
        (code?: string) => {
            if (!urlState.traceCodeId) {
                return Promise.reject();
            }
            return cSideRequests.getOrgTraceDataByTraceCodeId(
                urlState.traceCodeId,
                code,
            );
        },
        {
            manual: true, // 改为手动触发
            async onSuccess(res: any) {
                console.log("sorgp=", res);
                const result = res?.data?.data;
                const orgBasicRes = result?.orgBasic;
                const orgBriefRes = result?.orgBrief;
                console.log("orgssaaa", result);

                if (orgBasicRes?.qualification) {
                    const qualification = await decryptedUrl(
                        orgBasicRes?.qualification,
                    );
                    setOrgBasicQualification(qualification);
                }
                if (orgBriefRes?.picture) {
                    const arrayData = await Promise.all(
                        isArrayArr(orgBriefRes?.picture)?.map((item: any) => {
                            return decryptedUrl(item);
                        }),
                    );
                    setOrgBriefImg(arrayData);
                }
                const orgBriefVideo =
                    (await decryptedUrl(orgBriefRes?.video)) || "";
                setOrgBriefVideo(orgBriefVideo);
                //console.log('weweppp--setOrgBriefImg000', arrayData, qualification)
            },
        },
    );
    const traceCodeOrgDetail = traceCodeOrgDetailRequest.data?.data?.data || [];

    // if (!urlState.traceCodeId) {
    //     return <Navigate to="error?msg=找不到溯源码ID" replace></Navigate>
    // }

    if (
        checkInputPageRequest.loading ||
        (needInputPage === false && traceCodeDetailRequest.loading)
    ) {
        return (
            <div
                style={{
                    paddingTop: "200px",
                    textAlign: "center",
                    color: "var(--adm-color-weak)",
                }}
            >
                <div style={{ fontSize: 24, marginBottom: 24 }}>
                    <DotLoading />
                </div>
                正在加载数据
            </div>
        );
    }
    // 溯源信息
    const renderTraceTabInfo = () => {
        // 产品信息
        const productInfo = traceCodeDetail?.product || {};
        // 克（g） 千克（kg）
        const pic =
            productInfo.specificationUnit == "1" ? "克（g）" : "千克（kg）";
        const productDetailItems = [
            {
                label: "产品名称",
                value: productInfo.productName,
            },
            {
                label: "产品品类",
                value: productInfo.productCategory,
            },
            {
                label: "保质期",
                value: productInfo.expirationDate,
            },
            // {
            //     label: "产品编码",
            //     value: productInfo.productCode,
            // },
            {
                label: "食品生产许可证编号",
                value: productInfo.productionLicense,
            },
            {
                label: "产品执行标准",
                value: productInfo.executiveStandard,
            },
            {
                label: "产品单位",
                value: productInfo.productUnit,
            },
            {
                label: "产品规格",
                value:
                    productInfo.specification &&
                    productInfo.specification + pic,
            },
            {
                label: "配料",
                value: productInfo.ingredient,
            },
            // {
            //     custom: productInfo.productTransId && (
            //         <Link
            //             className="tolink"
            //             to={`certificate/${productInfo.productTransId}`}
            //         >
            //             {" "}
            //             <CheckCircleFill fontSize={18} /> 中移链溯源认证通过，点击了解更多
            //         </Link>
            //     ),
            //     //  <div><button >该信息已上链</button></div>
            // },
            // {
            //     label: "产品合格证明",
            //     value: productInfo.productAptitude && (
            //         <PreviewImages
            //             images={[productInfo.productAptitude]}
            //         ></PreviewImages>
            //     ),
            // },
            // {
            //   label: "链上哈希",
            //   value: productInfo.productTransId && (
            //     <Link
            //       style={{}}
            //       to={`certificate/${productInfo.productTransId}`}
            //     >
            //       <Ellipsis
            //         content={productInfo.productTransId}
            //       ></Ellipsis>
            //     </Link>
            //   ),
            // },
        ];
        // 种植溯源
        const plantInfo = traceCodeDetail?.landPlantInfo || {};
        const plantDetailItems = [
            { label: "种植过程", value: "" },
            {
                label: "农作物类型",
                value: plantInfo.plantName,
            },
            {
                label: "播种时间",
                value:
                    plantInfo.sowTime &&
                    dayjs(plantInfo.sowTime).format("YYYY-MM-DD HH:mm:ss"),
            },
            // {
            //     label: "打药次数",
            //     value: plantInfo.plantName,
            // },
            // {
            //     label: "灌溉次数",
            //     value: plantInfo.plantName,
            // },
            // {
            //     label: "除草次数",
            //     value: plantInfo.plantName,
            // },
            // {
            //     label: "整地次数",
            //     value: plantInfo.plantName,
            // },
            // {
            //     label: "施肥次数",
            //     value: plantInfo.plantName,
            // },
            // {
            //     label: "中耕次数",
            //     value: plantInfo.plantName,
            // },
            {
                label: "收割时间",
                value:
                    plantInfo.harvestTime &&
                    dayjs(plantInfo.harvestTime).format("YYYY-MM-DD HH:mm:ss"),
            },
            {
                label: "种植地块",
                value: plantInfo.landName,
            },
            {
                label: "地块产量",
                value: plantInfo.harvestNum
                    ? plantInfo.harvestNum + "吨"
                    : plantInfo.harvestNum,
            },
        ];

        // 仓储溯源信息

        const warhouseInfoData = traceCodeDetail?.warhouseInfoDataTo || {};
        const warhouseInfoDataItems = [
            {
                label: "入库时间",
                value:
                    warhouseInfoData.inboundTime &&
                    dayjs(warhouseInfoData.inboundTime).format(
                        "YYYY-MM-DD HH:mm:ss",
                    ),
            },
            {
                label: "仓库名称",
                value: warhouseInfoData.warehouseName,
            },
            {
                label: "仓库地址",
                value: warhouseInfoData.warehouseAddress,
            },
        ];

        // 收购溯源信息

        const purchaseInfo = traceCodeDetail?.purchase || {};
        const purchaseDetailItems = [
            { label: "收购过程", value: "" },
            {
                label: "农户姓名",
                value: purchaseInfo.farmerName,
            },
            {
                label: "收购方",
                value: purchaseInfo.acquiringFirm,
            },
            {
                label: "收购商品",
                value: purchaseInfo.plantName,
            },
            {
                label: "收购重量",
                value: purchaseInfo.purchaseWeight
                    ? purchaseInfo.purchaseWeight + "吨"
                    : purchaseInfo.purchaseWeight,
            },
            {
                label: "收购时间",
                value:
                    purchaseInfo.purchaseTime &&
                    dayjs(purchaseInfo.purchaseTime).format(
                        "YYYY-MM-DD HH:mm:ss",
                    ),
            },
            // {
            //     label: "收割时间",
            //     value:
            //         plantInfo.harvestTime &&
            //         dayjs(plantInfo.harvestTime).format("YYYY-MM-DD HH:mm:ss"),
            // },
        ];
        //
        const materialInfo = traceCodeDetail?.material || [];
        const materialConfig = materialInfo.map((materialItem: any) => {
            return {
                title: "原料详情",
                items: [
                    {
                        label: "原料名称",
                        value: materialItem.materialName,
                    },
                    {
                        label: "原料采购批次",
                        value: materialItem.purchaseBatch,
                    },
                    {
                        label: "生产日期",
                        value:
                            materialItem.productionDate &&
                            dayjs(materialItem.productionDate).format(
                                "YYYY.MM.DD HH:mm:ss",
                            ),
                    },
                    {
                        label: "保质期",
                        value: materialItem.expiration,
                    },
                    {
                        label: "原料数量",
                        value: materialItem.count,
                    },
                    {
                        label: "原料规格",
                        value: materialItem.specification,
                    },
                    {
                        label: "原料合格证明",
                        value: materialItem.certificate && (
                            <PreviewImages
                                images={[materialItem.certificate]}
                            ></PreviewImages>
                        ),
                    },
                    {
                        label: "原料图片",
                        value: materialItem.materialImg && (
                            <PreviewImages
                                images={[materialItem.materialImg]}
                            ></PreviewImages>
                        ),
                    },
                    {
                        label: "供应商",
                        value: materialItem.supplier,
                    },
                    {
                        label: "链上哈希",
                        value: materialItem.purchaseTransId && (
                            <Link
                                style={{}}
                                to={`certificate/${materialItem.purchaseTransId}`}
                            >
                                <Ellipsis
                                    content={materialItem.purchaseTransId}
                                ></Ellipsis>
                            </Link>
                        ),
                    },
                ],
            };
        });

        //注释掉
        const processInfo = traceCodeDetail?.process || [];
        // 动态赋值
        const processConfig = processInfo.map(
            (processItem: any, index: number) => {
                // const processConfig = processInfo.map((processItem: any) => {
                return {
                    title: "生产过程",
                    items: [
                        {
                            label: "生产过程名称",
                            value: processItem.processName,
                        },
                        {
                            label: "生产过程说明",
                            value: processItem.processInstructions && (
                                <TextAreaView
                                    content={processItem.processInstructions}
                                ></TextAreaView>
                            ),
                        },
                        {
                            label: "生产过程图片",
                            value: processItem.processImg &&
                                processDecImg &&
                                processDecImg[index] &&
                                processDecImg[index].length > 0 && (
                                    <PreviewImages
                                        images={processDecImg[index]}
                                    ></PreviewImages>
                                    // <PreviewImages
                                    //     images={processItem.processImg}
                                    // ></PreviewImages>
                                ),
                        },
                        {
                            label: "生产过程视频",
                            value: processItem.processVideo &&
                                processDecVideo && (
                                    // <PreviewVideo
                                    //     src={processItem.processVideo}
                                    // ></PreviewVideo>
                                    <PreviewVideo
                                        preview={false}
                                        src={processDecVideo[index]}
                                        // src={CSvideo}
                                    ></PreviewVideo>
                                ),
                        },
                        // {
                        //   label: "链上哈希",
                        //   value: processItem.processTransId && (
                        //     <Link
                        //       style={{}}
                        //       to={`certificate/${processItem.processTransId}`}
                        //     >
                        //       <Ellipsis
                        //         content={processItem.processTransId}
                        //       ></Ellipsis>
                        //     </Link>
                        //   ),
                        // },

                        // {
                        //     custom: processItem.processTransId && (
                        //         <Link
                        //             className="tolink"
                        //             to={`certificate/${processItem.processTransId}`}
                        //         >
                        //             <CheckCircleFill fontSize={18} />{" "}
                        //             中移链溯源认证通过，点击了解更多
                        //         </Link>
                        //     ),
                        // },
                    ],
                };
            },
        );

        const productionInfo = traceCodeDetail?.production || {};
        processConfig.push({
            title: "生产加工详情",
            items: [
                {
                    label: "批次号",
                    value: productionInfo.productionBatch,
                },
                {
                    label: "数量",
                    value: productionInfo.amount,
                },
                {
                    label: "生产线",
                    value: productionInfo.line,
                },

                {
                    label: "种植户",
                    value: productionInfo.grower,
                },
                {
                    label: "生产班次",
                    value: productionInfo.shift,
                },
                {
                    custom: productionInfo.productionTransId && (
                        <Link
                            className="tolink"
                            to={`certificate/${productionInfo.productionTransId}`}
                            style={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                gap: "8px",
                            }}
                        >
                            <CheckCircleFill fontSize={18} />
                            中移链溯源认证通过，点击了解更多
                        </Link>
                    ),
                },
                // {
                //   label: "链上哈希",
                //   value: productionInfo.productionTransId && (
                //     <Link
                //       style={{}}
                //       to={`certificate/${productionInfo.productionTransId}`}
                //     >
                //       <Ellipsis
                //         content={productionInfo.productionTransId}
                //       ></Ellipsis>
                //     </Link>
                //   ),
                // },
            ],
        });

        const logisticsInfo = traceCodeDetail?.logistics || {};
        const logisticsDetailItems = [
            {
                label: "物流企业",
                value: logisticsInfo.loEnterprises,
            },
            {
                label: "物流单号",
                value: logisticsInfo.loNumber,
            },
            {
                label: "装货地点",
                value: logisticsInfo.loadingLocation,
            },
            {
                label: "运输方式",
                value:
                    logisticsInfo.transportationType &&
                    // @ts-ignore
                    TRANSPORTATION_TYPE_CONSTANTS
                        .TRANSPORTATION_TYPE_MAP_BY_VALUE[
                        logisticsInfo.transportationType
                    ].name,
            },
            {
                label: "链上哈希",
                value: logisticsInfo.logisticsTransId && (
                    <Link
                        style={{}}
                        to={`certificate/${logisticsInfo.logisticsTransId}`}
                    >
                        <Ellipsis
                            content={logisticsInfo.logisticsTransId}
                        ></Ellipsis>
                    </Link>
                ),
            },
            {
                label: "卸货地点",
                value: logisticsInfo.unloadingLocation,
            },
            {
                // 卸货的链上哈希
                label: "链上哈希",
                value: logisticsInfo.unLoadLogisticsTransId && (
                    <Link
                        style={{}}
                        to={`certificate/${logisticsInfo.unLoadLogisticsTransId}`}
                    >
                        <Ellipsis
                            content={logisticsInfo.unLoadLogisticsTransId}
                        ></Ellipsis>
                    </Link>
                ),
            },
        ];

        return (
            <>
                <TraceBlock
                    title="产品信息"
                    config={[
                        {
                            items: productDetailItems,
                        },
                    ]}
                    // custom={<div><Button>按钮</Button></div> }
                ></TraceBlock>
                {/* <TraceBlock
            title="原料溯源"
            config={materialConfig}
        ></TraceBlock> */}

                {Object.keys(plantInfo).length !== 0 ? (
                    <TraceBlock
                        title="种植溯源"
                        config={[
                            {
                                items: plantDetailItems,
                            },
                        ]}
                    ></TraceBlock>
                ) : (
                    ""
                )}
                {Object.keys(purchaseInfo).length !== 0 ? (
                    <TraceBlock
                        title="收购溯源"
                        config={[
                            {
                                items: purchaseDetailItems,
                            },
                        ]}
                    ></TraceBlock>
                ) : (
                    ""
                )}
                {Object.keys(warhouseInfoData).length !== 0 ? (
                    <TraceBlock
                        title="仓储溯源"
                        config={[
                            {
                                items: warhouseInfoDataItems,
                            },
                        ]}
                    ></TraceBlock>
                ) : (
                    ""
                )}

                <TraceBlock
                    title="生产溯源"
                    config={processConfig}
                    // custom={<div><Button>按钮</Button></div> }
                ></TraceBlock>

                {/* <TraceBlock
  title="物流溯源"
  config={[
    {
      items: logisticsDetailItems,
    },
  ]}
></TraceBlock> */}
            </>
        );
    };
    // 产品简介
    const renderProductTabInfo = () => {
        if (traceCodeProductDetailRequest.loading) {
            return (
                <div
                    style={{
                        paddingTop: "60px",
                        textAlign: "center",
                        color: "var(--adm-color-weak)",
                    }}
                >
                    <div style={{ fontSize: 24, marginBottom: 24 }}>
                        <DotLoading />
                    </div>
                    正在加载数据
                </div>
            );
        }
        const productBrief = traceCodeProductDetail?.productBrief || {};
        const productBriefItems = [
            {
                value: productBrief.productIntro && (
                    <div className={styles.introText} style={{ width: "100%" }}>
                        {productBrief.productIntro}
                    </div>
                ),
            },
            {
                value: productBrief.productVideo && (
                    <div
                        style={{
                            width: "100%",
                        }}
                    >
                        <PreviewVideo
                            preview={false}
                            src={video}
                        ></PreviewVideo>
                    </div>
                ),
            },
        ];
        if (productBrief.productImg && promotionPicData) {
            promotionPicData.forEach((img: string) => {
                productBriefItems.push({
                    value: (
                        <div
                            style={{
                                width: "100%",
                            }}
                        >
                            <PreviewImages
                                preview={false}
                                images={[img]}
                            ></PreviewImages>
                        </div>
                    ),
                });
            });
        }
        const productBriefConfig = [
            {
                items: productBriefItems,
            },
        ];

        const placeBrief = traceCodeProductDetail?.placeBrief || {};
        const placeBriefItems = [
            {
                value: placeBrief.placeIntro && (
                    <>
                        <div
                            className={styles.introText}
                            style={{ width: "100%" }}
                        >
                            {placeBrief.placeIntro}
                        </div>
                    </>
                ),
            },
            {
                value: placeBrief.placeVideo && (
                    <div
                        style={{
                            width: "100%",
                        }}
                    >
                        <PreviewVideo
                            preview={false}
                            src={pbVideo}
                        ></PreviewVideo>
                    </div>
                ),
            },
        ];
        if (pbImg) {
            pbImg.forEach((img: string) => {
                placeBriefItems.push({
                    value: (
                        <div
                            style={{
                                width: "100%",
                            }}
                        >
                            <PreviewImages
                                preview={false}
                                images={[img]}
                            ></PreviewImages>
                        </div>
                    ),
                });
            });
        }
        const placeBriefConfig = [
            {
                items: placeBriefItems,
            },
        ];

        const placeBasic = traceCodeProductDetail?.placeBasic || {};
        const placeBasicItems = [
            {
                label: "产地名称",
                value: placeBasic.placeName,
            },
            {
                label: "产地地址",
                value: placeBasic.placeAddress,
            },
            {
                label: "产地位置图",
                value: placeBasic.placeAptitude && pbApt && (
                    <PreviewImages images={[pbApt]}></PreviewImages>
                ),
            },
            {
                custom: placeBasic.placeTransId && (
                    <Link
                        className="tolink"
                        to={`certificate/${placeBasic.placeTransId}`}
                        style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            gap: "8px",
                        }}
                    >
                        <CheckCircleFill fontSize={18} />{" "}
                        中移链溯源认证通过，点击了解更多
                    </Link>
                ),
            },
            // {
            //   label: "链上哈希",
            //   value: placeBasic.placeTransId && (
            //     <Link
            //       style={{}}
            //       to={`certificate/${placeBasic.placeTransId}`}
            //     >
            //       <Ellipsis content={placeBasic.placeTransId}></Ellipsis>
            //     </Link>
            //   ),
            // },
        ];
        const placeBasicConfig = [
            {
                items: placeBasicItems,
            },
        ];

        if (
            isTraceBlockEmpty(productBriefConfig) &&
            isTraceBlockEmpty(placeBriefConfig) &&
            isTraceBlockEmpty(placeBasicConfig)
        ) {
            return (
                <ErrorBlock
                    image={<div></div>}
                    title="暂无数据"
                    description=""
                />
            );
        }

        return (
            <>
                <TraceBlock
                    title="产品说明"
                    config={productBriefConfig}
                ></TraceBlock>
                <TraceBlock
                    title="产地说明"
                    config={placeBriefConfig}
                ></TraceBlock>
                <TraceBlock
                    title="产地详情"
                    config={placeBasicConfig}
                ></TraceBlock>
            </>
        );
    };
    // 质检信息
    const renderInspectionTabInfo = () => {
        if (traceCodeInspectionDetailRequest.loading) {
            return (
                <div
                    style={{
                        paddingTop: "60px",
                        textAlign: "center",
                        color: "var(--adm-color-weak)",
                    }}
                >
                    <div style={{ fontSize: 24, marginBottom: 24 }}>
                        <DotLoading />
                    </div>
                    正在加载数据
                </div>
            );
        }

        const inspectionConfig = traceCodeInspectionDetail.map(
            (item: any, index: number) => {
                return {
                    title: "质检详情",
                    items: [
                        {
                            label: "质检内容",
                            value: item.inspectionContent,
                        },
                        {
                            label: "质检结果",
                            value: item.inspectionResults && (
                                <div>
                                    {item.inspectionResults === "1" && "合格"}
                                    {item.inspectionResults === "2" && "不合格"}
                                </div>
                            ),
                        },
                        {
                            label: "质检报告",
                            value: item.inspectionReport &&
                                inspectionReportDec && (
                                    <PreviewImages
                                        images={[inspectionReportDec[index]]}
                                    ></PreviewImages>
                                ),
                        },
                        {
                            label: "质检机构",
                            value:
                                item.inspectionInstitution &&
                                item.inspectionInstitution,
                        },
                        {
                            custom: item.inspectionTransId && (
                                <Link
                                    className="tolink"
                                    to={`certificate/${item.inspectionTransId}`}
                                    style={{
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        gap: "8px",
                                    }}
                                >
                                    <CheckCircleFill fontSize={18} />{" "}
                                    中移链溯源认证通过，点击了解更多
                                </Link>
                            ),
                        },
                        // {
                        //     label: "链上哈希",
                        //     value: item.inspectionTransId && (
                        //         <Link
                        //             style={{}}
                        //             to={`certificate/${item.inspectionTransId}`}
                        //         >
                        //             <Ellipsis
                        //                 content={item.inspectionTransId}
                        //             ></Ellipsis>
                        //         </Link>
                        //     ),
                        // },
                    ],
                };
            },
        );

        if (isTraceBlockEmpty(inspectionConfig)) {
            return (
                <ErrorBlock
                    image={<div></div>}
                    title="暂无数据"
                    description=""
                />
            );
        }

        return (
            <>
                <TraceBlock
                    title="质检信息"
                    config={inspectionConfig}
                ></TraceBlock>
            </>
        );
    };
    // 生产商
    const renderOrgTabInfo = () => {
        if (traceCodeOrgDetailRequest.loading) {
            return (
                <div
                    style={{
                        paddingTop: "60px",
                        textAlign: "center",
                        color: "var(--adm-color-weak)",
                    }}
                >
                    <div style={{ fontSize: 24, marginBottom: 24 }}>
                        <DotLoading />
                    </div>
                    正在加载数据
                </div>
            );
        }

        const orgBrief = traceCodeOrgDetail.orgBrief || {};
        const orgBriefItems = [
            {
                value: orgBrief.introduce && (
                    <div className={styles.introText} style={{ width: "100%" }}>
                        {orgBrief.introduce}
                    </div>
                ),
            },
            {
                value: orgBrief.video && orgBriefVideo && (
                    <div
                        style={{
                            width: "100%",
                        }}
                    >
                        <PreviewVideo
                            preview={false}
                            src={orgBriefVideo}
                        ></PreviewVideo>
                    </div>
                ),
            },
        ];
        if (orgBrief.picture && orgBriefImg) {
            orgBriefImg.forEach((img: string) => {
                orgBriefItems.push({
                    value: (
                        <div
                            style={{
                                width: "100%",
                            }}
                        >
                            <PreviewImages
                                preview={false}
                                images={[img]}
                            ></PreviewImages>
                        </div>
                    ),
                });
            });
        }
        const orgBriefConfig = [
            {
                items: orgBriefItems,
            },
        ];

        const orgBasic = traceCodeOrgDetail?.orgBasic || {};
        const orgBasicItems = [
            {
                label: "企业名称",
                value: orgBasic.companyName,
            },
            {
                label: "企业地址",
                value: orgBasic.address,
            },
            {
                label: "统一社会信用代码",
                value: orgBasic.creditCode,
            },
            {
                label: "企业资质",
                value: orgBasic.qualification && orgBasicQualification && (
                    <PreviewImages
                        images={[orgBasicQualification]}
                    ></PreviewImages>
                ),
            },
            {
                custom: orgBasic.orgTransId && (
                    <Link
                        className="tolink"
                        to={`certificate/${orgBasic.orgTransId}`}
                        style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            gap: "8px",
                        }}
                    >
                        <CheckCircleFill fontSize={18} />{" "}
                        中移链溯源认证通过，点击了解更多
                    </Link>
                ),
            },
            // {
            //   label: "链上哈希",
            //   value: orgBasic.orgTransId && (
            //     <Link style={{}} to={`certificate/${orgBasic.orgTransId}`}>
            //       <Ellipsis content={orgBasic.orgTransId}></Ellipsis>
            //     </Link>
            //   ),
            // },
        ];
        const orgBasicConfig = [
            {
                items: orgBasicItems,
            },
        ];

        if (
            isTraceBlockEmpty(orgBriefConfig) &&
            isTraceBlockEmpty(orgBasicConfig)
        ) {
            return (
                <ErrorBlock
                    image={<div></div>}
                    title="暂无数据"
                    description=""
                />
            );
        }
        return (
            <>
                <TraceBlock title="生产商" config={orgBriefConfig}></TraceBlock>
                <TraceBlock
                    title="企业基础信息"
                    config={orgBasicConfig}
                ></TraceBlock>
            </>
        );
    };
    const back = () => {
        // 如果不需要输入页面，直接进入内容页面
        setShowHome(true);

        // 如果是验证码验证成功后点击进入，调用四个接口并传递验证码
        // if (verifiedCode) {
        //     traceCodeDetailRequest.run(verifiedCode);
        //     traceCodeProductDetailRequest.run(verifiedCode);
        //     traceCodeInspectionDetailRequest.run(verifiedCode);
        //     traceCodeOrgDetailRequest.run(verifiedCode);
        // } else {
        //     // 如果没有验证码，直接调用接口（不需要验证码的情况）
        //     traceCodeDetailRequest.run();
        //     traceCodeProductDetailRequest.run();
        //     traceCodeInspectionDetailRequest.run();
        //     traceCodeOrgDetailRequest.run();
        // }

        // 启动定时器逻辑
        const data = traceCodeDetail;
        if (data.userFeedback == 1 && data.searchCount <= 3) {
            if (ratingTimerRef.current) {
                clearTimeout(ratingTimerRef.current);
            }
            const timer = setTimeout(() => {
                setShowRatingPopup(true);
                form.resetFields();
                setSelectedOptions([]);
            }, 60000);
            ratingTimerRef.current = timer;
        }
    };

    const handleInputSuccess = () => {
        setShowInputPage(false);
        setShowHome(false); // 先不显示溯源信息页面

        // 清空输入框和相关状态
        setInputValue("");
        setInputError("");
        setShowKeyboard(false);

        // 验证成功后，先显示首页图片和"点击进入"按钮
        // 不立即调用四个接口，等用户点击"点击进入"按钮后再调用

        // 点击进入按钮时启动定时器
        const data = traceCodeDetail;
        if (data.userFeedback == 1 && data.searchCount <= 3) {
            // 清除之前的定时器（如果存在）
            if (ratingTimerRef.current) {
                clearTimeout(ratingTimerRef.current);
            }

            // 创建新的定时器
            const timer = setTimeout(() => {
                setShowRatingPopup(true);
                // 清空表单的值
                form.resetFields();
                // setRating(2.5);
                setSelectedOptions([]);
            }, 60000); // 1分钟 = 60000毫秒

            // 将timer存储在ref中
            ratingTimerRef.current = timer;
        }
    };

    const handleInputSubmit = () => {
        if (!inputValue.trim()) {
            setInputError("请输入6位防伪验证码");
            return;
        }
        if (inputValue.trim().length !== 6) {
            setInputError("请输入6位防伪验证码");
            return;
        }

        // 调用验证码验证接口
        verifyCodeRequest.run({
            id: urlState.traceCodeId,
            code: inputValue.trim(),
        });
    };

    const handleInputBack = () => {
        setShowInputPage(false);
        setInputValue("");
        setInputError("");
    };
    // 关闭视频
    const closeVideo = () => {
        console.log(11111111111111111111);
        setShowVideo(false);

        if (VideoRef.current) {
            // 1. 暂停音频
            VideoRef.current.stopVideo();
            // 2. 获取视频进度
            const progress = VideoRef.current.getProgress();
            console.log(VideoRef.current.getProgress());
            // 3. 隐藏视频
            setShowVideo(false);
            // 4. 打开音频
            setAudio(true);
            // 5. 播放音频
            if (AudioRef.current) {
                // 6. 设置进度
                AudioRef.current.currentTime = progress;
                // 7. 播放音频
                AudioRef.current.play();
            }
        }
    };
    // 关闭音频
    // const closeAudio = () => {
    //     setAudio(false);
    //     AudioRef.current.pause();
    // };

    const onClickMa = () => {
        // 清除评价定时器
        if (ratingTimerRef.current) {
            clearTimeout(ratingTimerRef.current);
            ratingTimerRef.current = null;
        }

        setShowRatingPopup(true);
        // 清空表单的值
        form.resetFields();
        setRating(0);
        setSelectedOptions([]);
    };

    const handleRateChange = (val: number) => {
        console.log(val);

        setRating(val);
        setShowFeedbackForm(true);
    };

    const closeRatingPopup = () => {
        setShowRatingPopup(false);
        // setRating(2.5);
        setShowFeedbackForm(false);
        setSelectedOptions([]);
    };
    // 意见反馈提交
    const options = [
        {
            label: "溯源信息更丰富",
            value: "1",
        },
        {
            label: "界面排版更美观",
            value: "2",
        },
        {
            label: "优化扫码流程",
            value: "3",
        },
        {
            label: "商品质量",
            value: "4",
        },
        {
            label: "其它或建议",
            value: "5",
        },
    ];
    const handleSubmitFeedback = () => {
        // Reset error
        setFeedbackError("");
        console.log(rating, "rating");
        if (rating == 0) return Toast.show("您还没有选择星级评分");
        if (selectedOptions.length == 0 && rating <= 4)
            return Toast.show("您还没有选择意见");
        form.validateFields()
            .then(async (values) => {
                // 验证商品质量描述
                console.log("表单验证成功", values);
                console.log(
                    "表单验证成功11111",
                    rating,
                    "12313",
                    selectedOptions,
                );

                showConfirmModal({
                    title: "反馈提交",
                    content: "提交后不可修改，请仔细核对信息无误后提交。",
                    // confirmBtnProps: {
                    //     color: "danger",
                    // },
                    async onConfirm() {
                        const value = {
                            timestamp: Date.now().toString(),
                            traceCodeId: urlState.traceCodeId,
                            score: rating,
                            phone:
                                rating > 4 || !values.phone
                                    ? null
                                    : values.phone,
                            systemFeedback:
                                rating > 4 ? null : values.otherSuggestion,
                            productFeedback:
                                rating > 4 ? null : values.productQuality,
                            scoreType:
                                rating > 4 ? null : selectedOptions.join(","),
                        } as const;
                        const signedData = await requestDataSignC(
                            value,
                            "userRatingAddVo",
                        );
                        productiveProcessEntryRequest.run({
                            userRatingAddVo: value,
                        });
                        console.log(signedData, "signedData");

                        // deprecateTransportationRequest.run();
                        // setShowConfirmDialog(false);
                        // setShowSuccessDialog(true);
                        // setTimeout(() => {
                        //     setShowSuccessDialog(false);
                        //     router.push("/c/trace");
                        // }, 2000);
                    },
                });
                // Show confirmation dialog
                // setShowConfirmDialog(true);
            })
            .catch((error) => {
                console.log("表单验证失败:", error);
            });
    };

    // const handleConfirmSubmit = () => {
    //     const values = form.getFieldsValue();

    //     // Reset all states
    //     setShowConfirmDialog(false);
    //     setShowRatingPopup(false);
    //     setRating(2.5);
    //     setShowFeedbackForm(false);
    //     setSelectedOptions([]);
    //     setFeedbackError("");
    //     form.resetFields();

    //     // Navigate back to consumer query page
    //     navigate("/consumer-query");
    // };

    const handleCancelSubmit = () => {
        setShowConfirmDialog(false);
    };

    return (
        <div>
            <div
                className={classNames({
                    // showHome?'none':'block'
                    none: showHome || showInputPage || needInputPage === null,
                    block:
                        !showHome && !showInputPage && needInputPage !== null,
                    imgbox: true,
                })}
            >
                {/* <h1 onClick={back}>首页</h1> */}
                <Image src={homeImg} fit="cover" />
                <div className="trace_btnc" onClick={back}>
                    <Button color="primary" fill="none">
                        点击进入
                    </Button>
                </div>
            </div>

            {/* 输入页面 */}
            <div
                className={classNames({
                    _global_page: true,
                    block: showInputPage,
                    none: !showInputPage,
                })}
                style={{
                    background: "#fff9f1",
                    position: "relative",
                    height: "100vh",
                    // padding: "20px",
                }}
            >
                <div
                    style={{
                        fontSize: "20px",
                        color: "#333",
                        marginBottom: "20px",
                        textAlign: "center",
                        paddingTop: "20px",
                    }}
                >
                    可信全链路溯源
                </div>
                <div className={styles.dic}>
                    <div className={styles.dicTitle}>&nbsp;&nbsp; 产品名称</div>

                    {/* 输入框区域 */}
                    <div
                        style={{
                            marginBottom: "20px",
                            padding: "0 5px",
                        }}
                        onClick={() => setShowKeyboard(true)}
                    >
                        <div className={styles.dicName}>{productNameText}</div>
                        <div
                            style={{
                                fontSize: "14px",
                                color: "#666",
                                marginBottom: "10px",
                                marginTop: "30px",
                                textAlign: "left",
                                paddingLeft: "16px",
                            }}
                        >
                            刮开涂层，输入6位防伪验证码
                        </div>
                        <PasscodeInput
                            ref={passcodeInputRef}
                            seperated
                            plain
                            value={inputValue}
                            onChange={setInputValue}
                            length={6}
                            style={
                                {
                                    "--cell-size": "min(12vw, 55px)",
                                    "--border-radius": "6px",
                                } as any
                            }
                        />
                    </div>

                    {inputError && (
                        <div
                            style={{
                                color: "#ff4d4f",
                                fontSize: "14px",
                                marginBottom: "20px",
                                textAlign: "center",
                            }}
                        >
                            {inputError}
                        </div>
                    )}

                    {verifyCodeRequest.loading && (
                        <div
                            style={{
                                color: "#1890ff",
                                fontSize: "14px",
                                marginBottom: "20px",
                                textAlign: "center",
                            }}
                        >
                            正在验证防伪码...
                        </div>
                    )}

                    {/* 功能图标区域 */}
                    <div className={styles.traceFunction}>
                        <div style={{ display: "flex" }}>
                            <img src={one} alt="" />
                            <div style={{ fontSize: "14px", color: "#FEB300" }}>
                                一屏全面展示
                            </div>
                        </div>

                        <div style={{ display: "flex" }}>
                            <img src={tow} alt="" />
                            <div style={{ fontSize: "14px", color: "#FEB300" }}>
                                一体平台配置
                            </div>
                        </div>

                        <div style={{ display: "flex" }}>
                            <img src={there} alt="" />
                            <div style={{ fontSize: "14px", color: "#FEB300" }}>
                                一码首尾溯源
                            </div>
                        </div>

                        <div style={{ display: "flex" }}>
                            <img src={four} alt="" />
                            <div style={{ fontSize: "14px", color: "#FEB300" }}>
                                一链可信监督
                            </div>
                        </div>
                    </div>
                    {/* NumberKeyboard 组件 */}

                    <NumberKeyboard
                        visible={showKeyboard}
                        onInput={(value) => {
                            console.log("我进来了", value);
                            if (inputValue.length < 6) {
                                const newValue = inputValue + value;
                                setInputValue(newValue);
                                setInputError("");

                                // 当输入满6位时自动提交验证
                                if (newValue.length === 6) {
                                    setTimeout(() => {
                                        // 调用验证码验证接口
                                        verifyCodeRequest.run({
                                            id: urlState.traceCodeId!,
                                            code: newValue.trim(),
                                        });
                                    }, 100); // 延迟100ms确保状态更新完成
                                }
                            }
                        }}
                        onDelete={() => {
                            setInputValue(inputValue.slice(0, -1));
                            setInputError("");
                        }}
                        // onClose={() => setShowKeyboard(false)}
                        showCloseButton={false}
                        style={{
                            // width: "100%",
                            maxWidth: "none",
                        }}
                    />
                </div>
            </div>
            <div
                className={classNames({
                    _global_page: true,
                    block: showHome,
                    none: !showHome,
                })}
            >
                {/* <div>

  <PreviewVideo
    className={styles.my_video_wrap}
    // preview={showVideo}
    preview={false}
    src={videoSrc}
  ></PreviewVideo>
  <audio controls>
    <source src={videoSrc} type="audio/mpeg" />
  </audio>
  <button onClick={closeVideo}>关闭视频</button>
</div> */}

                {
                    videoSrc && (
                        // <div className={styles.video_wrap + ' ' + (showVideo ? '' : 'none')} >

                        <>
                            {showVideo ? (
                                <div
                                    className={
                                        classNames({
                                            none: !showVideo,
                                        }) +
                                        " " +
                                        styles.video_wrap
                                    }
                                    style={{
                                        width: "100vw",
                                        maxWidth: "100%",
                                        margin: "0 auto",
                                    }}
                                >
                                    <span
                                        className={classNames({
                                            close: true,
                                            none: false,
                                        })}
                                        onClick={closeVideo}
                                    >
                                        X
                                    </span>

                                    <video
                                        style={{
                                            width: "100%",
                                            height: "30vh",
                                            background: "#000",
                                            objectFit: "contain",
                                        }}
                                        crossOrigin="anonymous"
                                        controls
                                        src={videoSrc}
                                        playsInline
                                        autoPlay={true}
                                        ref={VideoRef}
                                    ></video>
                                    {/* <PreviewVideo
                      // className={styles.my_video_wrap}
                      // preview={showVideo}
                      ref={VideoRef}
                            preview={false}
                      src={videoSrc}
                  ></PreviewVideo> */}
                                </div>
                            ) : (
                                ""
                            )}

                            {/* 音频 */}
                            {/* <div
                        className={classNames({
                            none: !showAudio,
                        })}
                            style={{
                            position: "fixed",
                            top: "65%",
                            right: "0%",
                            zIndex: 10,
                        }}
                    >
                        <span
                            style={{
                                position: "absolute",
                                right: "7px",
                                top: "-28px",
                                background: "#f1f3f4",
                                padding: "5px",
                                borderRadius: "13px",
                                color: "#000",
                                cursor: "pointer",
                            }}
                            // onClick={closeAudio}
                        >
                            关闭
                        </span>
                        <audio controls ref={AudioRef}>
                            <source src={videoSrc} type="audio/mpeg" />
                        </audio>
                    </div> */}
                        </>
                    )
                    //  </div>
                }
                <div className="_global_pageScrollContent">
                    <div className={styles.cTrace}>
                        <NavBar color="black" backArrow={false}>
                            溯源信息
                        </NavBar>
                        <div className={styles.traceFunction}>
                            {/* <div className={styles.traceFunction__info}>
                    <img
                        className={styles.traceFunction__icon}
                        src={iconWarning}
                    />
                    <span>
                        {traceCodeDetail.searchCount === 1
                            ? "正品认证 品质之选"
                            : "多次验证 谨防假冒"}
                    </span>
                    <span>
                        正品认证 品质之选
                    </span>
                </div> */}
                        </div>
                        <div className={styles.main}>
                            {traceCodeDetail.traceCodeType == 0 ? (
                                traceCodeDetail.scanStatus == 1 ? (
                                    <div className={styles.traceFunction__suss}>
                                        <div
                                            className={
                                                styles.traceFunction__img
                                            }
                                        ></div>
                                        {/* <img src={var(--sess-bg-img)} alt="" /> */}
                                        <div>
                                            <div>溯源验证通过</div>
                                            <div
                                                className={
                                                    styles.traceSearchInfo__value
                                                }
                                            >
                                                溯源码：{" "}
                                                {traceCodeDetail.code || "-"}
                                            </div>
                                            <div
                                                className={
                                                    styles.traceSearchInfo__value
                                                }
                                            >
                                                首次查询时间：
                                                {traceCodeDetail.firstTime
                                                    ? dayjs(
                                                          traceCodeDetail.firstTime,
                                                      ).format(
                                                          "YYYY.MM.DD HH:mm:ss",
                                                      )
                                                    : "-"}
                                            </div>
                                            <div
                                                className={
                                                    styles.traceSearchInfo__value
                                                }
                                            >
                                                总查询次数
                                                {traceCodeDetail.searchCount ||
                                                    0}
                                                次，点击
                                                <span
                                                    style={{
                                                        color: "#007EFF",
                                                    }}
                                                    onClick={() =>
                                                        setShowQueryRecords(
                                                            true,
                                                        )
                                                    }
                                                >
                                                    查看明细&gt;
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                ) : traceCodeDetail.maxQuantity >=
                                  traceCodeDetail.searchCount ? (
                                    <div className={styles.traceFunction__suss}>
                                        <div
                                            className={
                                                styles.traceFunction__img
                                            }
                                        ></div>
                                        {/* <img src={suss} alt="" /> */}
                                        <div>
                                            <div>溯源验证通过</div>
                                            <div
                                                className={
                                                    styles.traceSearchInfo__value
                                                }
                                            >
                                                溯源码：{" "}
                                                {traceCodeDetail.code || "-"}
                                            </div>
                                            <div
                                                className={
                                                    styles.traceSearchInfo__value
                                                }
                                            >
                                                首次查询时间：{" "}
                                                {traceCodeDetail.firstTime
                                                    ? dayjs(
                                                          traceCodeDetail.firstTime,
                                                      ).format(
                                                          "YYYY.MM.DD HH:mm:ss",
                                                      )
                                                    : "-"}
                                            </div>
                                            <div
                                                className={
                                                    styles.traceSearchInfo__value
                                                }
                                            >
                                                总查询次数
                                                {traceCodeDetail.searchCount ||
                                                    0}
                                                次，点击
                                                <span
                                                    style={{
                                                        color: "#007EFF",
                                                    }}
                                                    onClick={() =>
                                                        setShowQueryRecords(
                                                            true,
                                                        )
                                                    }
                                                >
                                                    查看明细&gt;
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                ) : (
                                    <div className={styles.traceFunction__info}>
                                        <div
                                            className={
                                                styles.traceFunction__info_img
                                            }
                                        ></div>
                                        {/* <img src={Info} alt="" /> */}
                                        <div>
                                            <div>风险提醒</div>
                                            <div
                                                className={
                                                    styles.traceFunction__info_text
                                                }
                                            >
                                                该码已被多次查验，建议您核实产品来源谨防假冒
                                            </div>

                                            <div
                                                className={
                                                    styles.traceSearchInfo__value
                                                }
                                            >
                                                溯源码：{" "}
                                                {traceCodeDetail.code || "-"}
                                            </div>
                                            <div
                                                className={
                                                    styles.traceSearchInfo__value
                                                }
                                            >
                                                首次查询时间：{" "}
                                                {traceCodeDetail.firstTime
                                                    ? dayjs(
                                                          traceCodeDetail.firstTime,
                                                      ).format(
                                                          "YYYY.MM.DD HH:mm:ss",
                                                      )
                                                    : "-"}
                                            </div>
                                            <div
                                                className={
                                                    styles.traceSearchInfo__value
                                                }
                                            >
                                                总查询次数
                                                {traceCodeDetail.searchCount ||
                                                    0}
                                                次，点击
                                                <span
                                                    style={{
                                                        color: "#007EFF",
                                                    }}
                                                    onClick={() =>
                                                        setShowQueryRecords(
                                                            true,
                                                        )
                                                    }
                                                >
                                                    查看明细&gt;
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                )
                            ) : traceCodeDetail.scanStatus == 1 ? (
                                <div className={styles.traceFunctionBg}>
                                    <div
                                        className={styles.traceFunction__suss1}
                                    >
                                        <div
                                            className={
                                                styles.traceFunction__img1
                                            }
                                        ></div>
                                        {/* <img src={var(--sess-bg-img)} alt="" /> */}
                                        <div>
                                            <div>溯源验证通过</div>
                                        </div>
                                    </div>
                                    <div
                                        className={
                                            styles.traceSearchInfo__value
                                        }
                                        style={{ marginLeft: "20%" }}
                                    >
                                        溯源码： {traceCodeDetail.code || "-"}
                                    </div>
                                </div>
                            ) : traceCodeDetail.maxQuantity >=
                              traceCodeDetail.searchCount ? (
                                <div className={styles.traceFunctionBg}>
                                    <div
                                        className={styles.traceFunction__suss1}
                                    >
                                        <div
                                            className={
                                                styles.traceFunction__img1
                                            }
                                        ></div>
                                        {/* <img src={var(--sess-bg-img)} alt="" /> */}
                                        <div>
                                            <div>溯源验证通过</div>
                                        </div>
                                    </div>
                                    <div
                                        className={
                                            styles.traceSearchInfo__value
                                        }
                                        style={{ marginLeft: "20%" }}
                                    >
                                        溯源码： {traceCodeDetail.code || "-"}
                                    </div>
                                </div>
                            ) : (
                                <div className={styles.traceFunctionBg}>
                                    <div
                                        className={styles.traceFunction__info1}
                                    >
                                        <div
                                            className={
                                                styles.traceFunction__info_img1
                                            }
                                        ></div>
                                        <div>
                                            <div>风险提醒</div>
                                            <div
                                                className={
                                                    styles.traceFunction__info_text
                                                }
                                            >
                                                该码已被多次查验，建议您核实产品来源谨防假冒
                                            </div>
                                        </div>

                                        {/* <img src={Info} alt="" /> */}
                                    </div>
                                    <div
                                        className={
                                            styles.traceSearchInfo__value
                                        }
                                        style={{ marginLeft: "20%" }}
                                    >
                                        溯源码： {traceCodeDetail.code || "-"}
                                    </div>
                                </div>
                            )}

                            {/* <div className={styles.traceSearchInfo}> */}
                            {/* <div className={styles.traceSearchInfo__item}>
                                  <div
                                      className={
                                          styles.traceSearchInfo__value
                                      }
                                  >
                                      溯源码 {traceCodeDetail.code || "-"}
                    </div>
                              </div> */}
                            {/* <div className={styles.traceSearchInfo__item}>
                            <div className={styles.traceSearchInfo__label}>
                                首次查询
                </div>
                            <div className={styles.traceSearchInfo__value}>
                                {" "}
                                {traceCodeDetail.firstTime
                                    ? dayjs(
                                          traceCodeDetail.firstTime,
                                      ).format("YYYY.MM.DD HH:mm:ss")
                                    : "-"}
                            </div>
                        </div>
                        <div className={styles.traceSearchInfo__item}>
                            <div className={styles.traceSearchInfo__label}>
                                扫码次数
                            </div>
                            <div className={styles.traceSearchInfo__value}>
                                {traceCodeDetail.searchCount !==
                                undefined ? (
                                    <Link
                                        to={`scan-log/${urlState.traceCodeId}`}
                                        className={
                                            styles.traceSearchInfo__link
                                        }
                                    >
                                        {traceCodeDetail.searchCount}次
                                        <RightOutline></RightOutline>
                                    </Link>
                                ) : (
                                    "-"
                                )}
                    </div>
                        </div> */}
                            {/* </div> */}
                            <Tabs
                                className={styles.traceTabs}
                                activeKey={traceTabsActiveKey}
                                onChange={(activeKey: string) => {
                                    const traceTabsKey =
                                        activeKey as TraceTabsKey;
                                    setTraceTabsActiveKey(traceTabsKey);
                                }}
                            >
                                <Tabs.Tab
                                    title="溯源信息"
                                    key={TraceTabsKey.trace}
                                >
                                    {renderTraceTabInfo()}
                                </Tabs.Tab>
                                <Tabs.Tab
                                    title="产品简介"
                                    key={TraceTabsKey.product}
                                >
                                    {renderProductTabInfo()}
                                </Tabs.Tab>
                                <Tabs.Tab
                                    title="质检信息"
                                    key={TraceTabsKey.inspection}
                                >
                                    {renderInspectionTabInfo()}
                                </Tabs.Tab>
                                <Tabs.Tab
                                    title="生产商"
                                    key={TraceTabsKey.enterprise}
                                >
                                    {renderOrgTabInfo()}
                                </Tabs.Tab>
                            </Tabs>
                            {/* 用户反馈 */}
                            <div>
                                {traceCodeDetail.userFeedback == 1 ? (
                                    <FloatingBubble
                                        style={{
                                            "--initial-position-bottom": "24px",
                                            "--initial-position-right": "24px",
                                            "--edge-distance": "24px",
                                            "--size": "56px",
                                            "--background": "transparent",
                                        }}
                                        onClick={onClickMa}
                                    >
                                        <div className={styles.feedback}>
                                            <img src={jian} alt="" />
                                            <div>意见反馈</div>
                                        </div>
                                        {/* <MessageFill fontSize={32} /> */}
                                    </FloatingBubble>
                                ) : (
                                    ""
                                )}
                            </div>
                            {/* 弹框 */}
                            {/* <Rate allowHalf defaultValue={2.5} /> */}
                        </div>
                    </div>
                </div>
            </div>
            {/* Custom Rating Dialog */}
            {showRatingPopup && (
                <div
                    style={{
                        position: "fixed",
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: "rgba(0,0,0,0.5)",
                        zIndex: 1000,
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                    // onClick={closeRatingPopup}
                >
                    <div
                        style={{
                            width: "85%",
                            maxWidth: "400px",
                            background: "white",
                            borderRadius: "5px",
                            padding: "10px 25px 10px 10px",
                            position: "relative",
                            maxHeight: "88vh",
                            overflow: "auto",
                        }}
                        className={styles.ratingPopup}
                        onClick={(e) => e.stopPropagation()}
                    >
                        <div
                            style={{
                                position: "absolute",
                                right: "10px",
                                top: "10px",
                                cursor: "pointer",
                                fontSize: "18px",
                            }}
                            onClick={closeRatingPopup}
                        >
                            <CloseOutline />
                        </div>

                        <div
                            style={{
                                marginBottom: "15px",
                                fontWeight: "bold",
                                textAlign: "center",
                                fontSize: "18px",
                            }}
                        >
                            你对本次溯源体验满意吗?
                        </div>

                        {
                            <Form
                                form={form}
                                layout="vertical"
                                className={styles.ratingForm}
                                style={{
                                    "--prefix-width": "6em",
                                    padding: "0",
                                }}
                                footer={
                                    <div>
                                        <Button
                                            block
                                            type="submit"
                                            color="primary"
                                            onClick={handleSubmitFeedback}
                                            style={{
                                                marginTop: "10px",
                                                "--background-color": "#FEB300",
                                                "--border-color": "#FEB300",
                                                marginLeft: "8px",
                                            }}
                                        >
                                            提交
                                        </Button>
                                    </div>
                                }
                            >
                                {/* 星级评分 */}
                                <div
                                    style={{
                                        display: "flex",
                                        justifyContent: "center",
                                        marginBottom: "15px",
                                    }}
                                >
                                    <Rate
                                        allowHalf
                                        value={rating}
                                        allowClear={false}
                                        style={{
                                            "--active-color": "#FFD21E",
                                        }}
                                        onChange={handleRateChange}
                                    />
                                </div>
                                {/* 建议 */}
                                {0 < rating && rating <= 4 && (
                                    <div>
                                        <div
                                            style={{
                                                // textAlign: "center",
                                                marginBottom: "10px",
                                                fontSize: "16px",
                                                paddingLeft: "10px",
                                                paddingRight: "2px",
                                            }}
                                        >
                                            <span
                                                style={{
                                                    color: "red",
                                                    fontSize: "18px",
                                                    fontFamily:
                                                        "SimSun, sans-serif",
                                                }}
                                            >
                                                *
                                            </span>
                                            希望以下方面能做得更好
                                        </div>
                                        <div className={styles["suggest"]}>
                                            {/* 第一行：两个选项 */}
                                            <Selector
                                                options={options.slice(0, 2)}
                                                multiple
                                                columns={2}
                                                style={{
                                                    "--checked-color":
                                                        "#FEB30080",
                                                    "--checked-text-color":
                                                        "#000",
                                                    marginBottom: "10px",
                                                    fontSize: "12px",
                                                }}
                                                showCheckMark={false}
                                                value={selectedOptions}
                                                onChange={(arr) => {
                                                    // 保留后三个选项的选择状态
                                                    const remainingOptions =
                                                        selectedOptions.filter(
                                                            (option) =>
                                                                option ===
                                                                    "3" ||
                                                                option ===
                                                                    "4" ||
                                                                option === "5",
                                                        );
                                                    // 合并数组并去重
                                                    const combinedOptions = [
                                                        ...arr,
                                                        ...remainingOptions,
                                                    ];
                                                    const uniqueOptions = [
                                                        ...new Set(
                                                            combinedOptions,
                                                        ),
                                                    ];
                                                    setSelectedOptions(
                                                        uniqueOptions,
                                                    );
                                                }}
                                            />

                                            {/* 第二行：三个选项 */}
                                            <Selector
                                                options={options.slice(2, 5)}
                                                multiple
                                                columns={3}
                                                style={{
                                                    "--checked-color":
                                                        "#FEB30080",
                                                    "--checked-text-color":
                                                        "#000",
                                                    "--padding": "8px 10px",
                                                    fontSize: "12px",
                                                }}
                                                showCheckMark={false}
                                                value={selectedOptions}
                                                onChange={(arr) => {
                                                    // 保留前两个选项的选择状态
                                                    const remainingOptions =
                                                        selectedOptions.filter(
                                                            (option) =>
                                                                option ===
                                                                    "1" ||
                                                                option === "2",
                                                        );
                                                    // 合并数组并去重
                                                    const combinedOptions = [
                                                        ...remainingOptions,
                                                        ...arr,
                                                    ];
                                                    const uniqueOptions = [
                                                        ...new Set(
                                                            combinedOptions,
                                                        ),
                                                    ];
                                                    setSelectedOptions(
                                                        uniqueOptions,
                                                    );
                                                }}
                                            />
                                        </div>

                                        {selectedOptions.includes("4") && (
                                            <Form.Item
                                                label="商品质量描述"
                                                name="productQuality"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: "",
                                                    },
                                                    () => ({
                                                        validator: (
                                                            _: any,
                                                            value: any,
                                                            callback: any,
                                                        ) => {
                                                            if (!value) {
                                                                callback(
                                                                    "您的意见对我们非常重要",
                                                                );
                                                            } else if (
                                                                value[0] ==
                                                                    " " ||
                                                                value[
                                                                    value.length -
                                                                        1
                                                                ] == " "
                                                            ) {
                                                                callback(
                                                                    "字段前后不能输入空格！",
                                                                );
                                                            } else {
                                                                // 使用trim去掉前后空格
                                                                const trimmedValue =
                                                                    value.trim();
                                                                if (
                                                                    !trimmedValue
                                                                ) {
                                                                    callback(
                                                                        "您的意见对我们非常重要!",
                                                                    );
                                                                } else {
                                                                    callback();
                                                                }
                                                            }
                                                        },
                                                    }),
                                                ]}
                                            >
                                                <TextArea
                                                    rows={2}
                                                    maxLength={50}
                                                    placeholder="您的意见对我们非常重要"
                                                    style={{
                                                        "--color": "#333",
                                                        "--placeholder-color":
                                                            "#999",
                                                        "--border":
                                                            "1px solid #eee",
                                                        "--border-radius":
                                                            "4px",
                                                        "--font-size": "14px",
                                                        padding: "8px",
                                                        backgroundColor:
                                                            "#f5f5f5",
                                                        boxShadow: "none",
                                                    }}
                                                    // onChange={(val) => {
                                                    //     // 只去除首尾空格
                                                    //     const trimmedValue =
                                                    //         val.trim();
                                                    //     if (
                                                    //         val !== trimmedValue
                                                    //     ) {
                                                    //         form.setFieldValue(
                                                    //             "productQuality",
                                                    //             trimmedValue,
                                                    //         );
                                                    //     }
                                                    // }}
                                                ></TextArea>
                                            </Form.Item>
                                        )}

                                        {selectedOptions.includes("5") && (
                                            <Form.Item
                                                label="其它或建议"
                                                name="otherSuggestion"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: "",
                                                    },
                                                    () => ({
                                                        validator: (
                                                            _: any,
                                                            value: any,
                                                            callback: any,
                                                        ) => {
                                                            if (!value) {
                                                                callback(
                                                                    "您的意见对我们非常重要",
                                                                );
                                                            } else if (
                                                                value[0] ==
                                                                    " " ||
                                                                value[
                                                                    value.length -
                                                                        1
                                                                ] == " "
                                                            ) {
                                                                callback(
                                                                    "字段前后不能输入空格！",
                                                                );
                                                            } else {
                                                                // 使用trim去掉前后空格
                                                                const trimmedValue =
                                                                    value.trim();
                                                                if (
                                                                    !trimmedValue
                                                                ) {
                                                                    callback(
                                                                        "您的意见对我们非常重要!",
                                                                    );
                                                                } else {
                                                                    callback();
                                                                }
                                                            }
                                                        },
                                                    }),
                                                ]}
                                            >
                                                <TextArea
                                                    rows={2}
                                                    maxLength={50}
                                                    placeholder="您的意见对我们非常重要"
                                                    style={{
                                                        "--color": "#333",
                                                        "--font-size": "14px",
                                                        "--placeholder-color":
                                                            "#999",
                                                        "--border":
                                                            "1px solid #eee",
                                                        "--border-radius":
                                                            "4px",
                                                        padding: "8px",
                                                        backgroundColor:
                                                            "#f5f5f5",
                                                        boxShadow: "none",
                                                    }}
                                                    // onChange={(val) => {
                                                    //     // 只去除首尾空格
                                                    //     const trimmedValue =
                                                    //         val.trim();
                                                    //     if (
                                                    //         val !== trimmedValue
                                                    //     ) {
                                                    //         form.setFieldValue(
                                                    //             "otherSuggestion",
                                                    //             trimmedValue,
                                                    //         );
                                                    //     }
                                                    // }}
                                                ></TextArea>
                                            </Form.Item>
                                        )}
                                        {(selectedOptions.includes("4") ||
                                            selectedOptions.includes("5")) && (
                                            <Form.Item
                                                label="联系电话"
                                                name="phone"
                                                rules={[
                                                    {
                                                        required: false,
                                                        message:
                                                            "请填写联系电话",
                                                    },
                                                    {
                                                        pattern:
                                                            /^1[3-9]\d{9}$/,
                                                        message:
                                                            "请输入正确的手机号码",
                                                    },
                                                ]}
                                            >
                                                <Input
                                                    type="tel"
                                                    placeholder="请输入您的联系电话"
                                                    style={{
                                                        "--font-size": "14px",
                                                        "--color": "#333",
                                                        "--placeholder-color":
                                                            "#999",
                                                        "--border":
                                                            "1px solid #eee",
                                                        "--border-radius":
                                                            "4px",
                                                        padding: "8px",
                                                        backgroundColor:
                                                            "#f5f5f5",
                                                    }}
                                                />
                                            </Form.Item>
                                        )}
                                    </div>
                                )}

                                {feedbackError && (
                                    <div
                                        style={{
                                            color: "red",
                                            fontSize: "12px",
                                            marginBottom: "10px",
                                            textAlign: "center",
                                        }}
                                    >
                                        {feedbackError}
                                    </div>
                                )}
                            </Form>
                        }
                    </div>
                </div>
            )}

            {/* 查询记录弹框 */}
            {showQueryRecords && (
                <div
                    style={{
                        position: "fixed",
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: "rgba(0,0,0,0.5)",
                        zIndex: 1000,
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                    onClick={() => setShowQueryRecords(false)}
                >
                    <div
                        style={{
                            width: "70%",
                            maxWidth: "400px",
                            background: "#ffffff",
                            borderRadius: "8px",
                            position: "relative",
                            maxHeight: "80vh",
                            overflow: "auto",
                        }}
                        onClick={(e) => e.stopPropagation()}
                    >
                        <div
                            style={{
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                                color: "white",
                                padding: "15px 25px",
                                background: "#FEB300",
                            }}
                        >
                            <div
                                style={{
                                    fontWeight: "bold",
                                    fontSize: "18px",
                                }}
                            >
                                查询记录
                            </div>
                            <div
                                style={{
                                    cursor: "pointer",
                                    fontSize: "18px",
                                    fontWeight: 600,
                                }}
                                onClick={() => setShowQueryRecords(false)}
                            >
                                <CloseOutline />
                            </div>
                        </div>

                        <div
                            style={{
                                background: "white",
                                borderRadius: "5px",
                                maxHeight: "60vh",
                                overflow: "auto",
                                padding: " 0 10px",
                                fontSize: "14px",
                            }}
                        >
                            {queryRecords.map((time: any, index: any) => (
                                <div
                                    key={index}
                                    style={{
                                        padding: "12px 15px",
                                        borderBottom:
                                            index !== queryRecords.length - 1
                                                ? "1px solid #e0e0e0"
                                                : "none",
                                    }}
                                >
                                    <div>
                                        查询时间:{" "}
                                        {dayjs(time.optTime).format(
                                            "YYYY-MM-DD HH:mm:ss",
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>

                        <div
                            style={{
                                display: "flex",
                                justifyContent: "end",
                                marginTop: "15px",
                                marginBottom: "15px",
                            }}
                        >
                            <button
                                style={{
                                    padding: "8px 12px",
                                    background: "#969696",
                                    border: "none",
                                    borderRadius: "4px",
                                    marginRight: "10px",
                                    cursor: "pointer",
                                    color: "white",
                                    fontSize: "14px",
                                    width: "60px",
                                }}
                                onClick={() => setShowQueryRecords(false)}
                            >
                                关闭
                            </button>
                            <button
                                style={{
                                    padding: "8px 12px",
                                    background: "#FEB300",
                                    border: "none",
                                    borderRadius: "4px",
                                    marginRight: "10px",
                                    cursor: "pointer",
                                    color: "white",
                                    fontSize: "14px",
                                    width: "60px",
                                }}
                                onClick={() => setShowQueryRecords(false)}
                            >
                                确定
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Trace;

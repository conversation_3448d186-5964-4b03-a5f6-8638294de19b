import { useRef, useEffect, useState } from "react";
import {
    <PERSON>ton,
    Input,
    TextArea,
    Picker,
    Space,
    Modal,
    Toast,
    Dialog,
    Form,
} from "antd-mobile";
import {
    PhoneFill,
    LockFill,
    EyeInvisibleOutline,
    EyeOutline,
} from "antd-mobile-icons";
import JSEncrypt from "jsencrypt";
import CryptoJS from "crypto-js";
import md5 from "js-md5";
import ecc from "eosjs-ecc";

import request from "@/services";
import useStore from "@/store";

import type { InputRef } from "antd-mobile";

export async function rsaEncrypt(str: string, pK: string) {
    try {
        const encryptor = new JSEncrypt();
        encryptor.setPublicKey(pK);
        const rsaPassWord = encryptor.encrypt(str);
        return rsaPassWord || "";
    } catch (err) {
        Toast.show({
            content: "请求加密失败",
        });
        console.log(err, "rsaEncrypt err");
        return Promise.reject();
    }
}

const encryptedIv = "tNM33MyJcLt0tCHv";

/**
 * 加密方法-此方法外部无需调用
 * @param privatekey 加密后的私钥
 * @param encryptKey 口令密码
 * @returns
 */
function decrypt(privatekey: string, encryptKey: string) {
    // @ts-ignore
    const passwordMd5 = md5(encryptKey);
    const key = CryptoJS.enc.Utf8.parse(passwordMd5);
    const iv = CryptoJS.enc.Utf8.parse(encryptedIv); //十六位十六进制数作为密钥偏移量
    let encryptedHexStr = CryptoJS.enc.Hex.parse(privatekey);
    let srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr);
    let decrypt = CryptoJS.AES.decrypt(srcs, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
    });
    let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
    return decryptedStr.toString();
}

export function decryptPrivatekey(encryptedPrivateKey: string) {
    return new Promise<string>((resolve, reject) => {
        let owerWord = "";
        const FormContent = () => {
            const [visible, setVisible] = useState(false);
            return (
                <Form
                    layout="horizontal"
                    style={{
                        "--border-bottom": "none",
                        "--border-top": "none",
                    }}
                >
                    <Form.Item
                        required={false}
                        name="password"
                        rules={[
                            { required: true, message: "口令密码不能为空" },
                        ]}
                        extra={
                            <div className="eye">
                                {!visible ? (
                                    <EyeInvisibleOutline
                                        onClick={() => setVisible(true)}
                                    />
                                ) : (
                                    <EyeOutline
                                        onClick={() => setVisible(false)}
                                    />
                                )}
                            </div>
                        }
                    >
                        <Input
                            onChange={(value) => {
                                owerWord = value;
                            }}
                            type={visible ? "text" : "password"}
                            placeholder="请输入口令密码"
                        />
                    </Form.Item>
                </Form>
            );
        };
        Dialog.confirm({
            title: "口令密码",
            content: <FormContent></FormContent>,
            onConfirm() {
                if (owerWord.length === 0) {
                    Toast.show("请输入口令密码");
                    return Promise.reject();
                }
                try {
                    const privateKey = decrypt(encryptedPrivateKey, owerWord);
                    if (privateKey.length > 0) {
                        resolve(privateKey);
                    } else {
                        // reject("解密私钥失败");
                        reject("口令密码错误");
                    }
                } catch (err) {
                    // reject("解密私钥失败");
                    reject("口令密码错误");
                }
            },
            onCancel() {
                reject("请输入口令密码");
            },
        });
    });
}

export const signatureByEncryptedPrivateKey = async (str: string) => {
    const encryptedPrivateKey = useStore.getState()?.userInfo?.privateKey;
    if (!encryptedPrivateKey) {
        throw new Error("找不到私钥");
    }
    const decryptedPrivateKey = useStore.getState()?.decryptedPrivateKey;
    if (decryptedPrivateKey) {
        try {
            return ecc.sign(str, decryptedPrivateKey);
        } catch (err) {}
    }
    const pK = await decryptPrivatekey(encryptedPrivateKey);
    useStore.setState((state) => {
        return {
            ...state,
            decryptedPrivateKey: pK,
        };
    });
    return ecc.sign(str, pK);
};

export async function requestDataSign(requestData: any, dataKey: any) {
    const requestDataJson = JSON.stringify(requestData);
    const signedRequestData = {
        [dataKey]: requestData,
        paramStr: requestDataJson,
        signature: await signatureByEncryptedPrivateKey(requestDataJson),
    };
    return signedRequestData as any;
}
//  c端加密写死
function decryptC(privatekey: string, encryptKey: string) {
    // @ts-ignore
    const passwordMd5 = md5(encryptKey);
    const key = CryptoJS.enc.Utf8.parse(passwordMd5);
    const iv = CryptoJS.enc.Utf8.parse(encryptedIv); //十六位十六进制数作为密钥偏移量
    let encryptedHexStr = CryptoJS.enc.Hex.parse(privatekey);
    let srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr);
    let decrypt = CryptoJS.AES.decrypt(srcs, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
    });
    let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
    return decryptedStr.toString();
}

export const signatureByEncryptedPrivateKeyC = async (str: string) => {
    // const encryptedPrivateKey = useStore.getState()?.userInfo?.privateKey;
    const encryptedPrivateKey =
        "099BB19681DC99514B7825322F4531112109E8BA78BF28F5799386257ADC1FA2EC2D4A266BF5DB9403929153A937209EE4F936A77A6946075313ECAB67C113F6";
    if (!encryptedPrivateKey) {
        throw new Error("找不到私钥");
    }
    const decryptedPrivateKey = useStore.getState()?.decryptedPrivateKey;
    if (decryptedPrivateKey) {
        try {
            return ecc.sign(str, decryptedPrivateKey);
        } catch (err) {}
    }
    const pK = await decryptC(encryptedPrivateKey, "Abc1234%");
    useStore.setState((state) => {
        return {
            ...state,
            decryptedPrivateKey: pK,
        };
    });
    return ecc.sign(str, pK);
};
export async function requestDataSignC(requestData: any, dataKey: any) {
    const requestDataJson = JSON.stringify(requestData);
    const signedRequestData = {
        [dataKey]: requestData,
        paramStr: requestDataJson,
        signature: await signatureByEncryptedPrivateKeyC(requestDataJson),
    };
    return signedRequestData as any;
}

export async function randomPassword(length: number) {
    length = Number(length);
    // Limit length
    if (length < 6) {
        length = 6;
    } else if (length > 16) {
        length = 16;
    }
    let passwordArray = [
        "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
        "abcdefghijklmnopqrstuvwxyz",
        "1234567890",
        "!#$%",
    ];
    var password = [];
    let n = 0;
    for (let i = 0; i < length; i++) {
        // If password length less than 9, all value random
        if (password.length < length - 4) {
            // Get random passwordArray index
            let arrayRandom = Math.floor(Math.random() * 4);
            // Get password array value
            let passwordItem = passwordArray[arrayRandom];
            // Get password array value random index
            // Get random real value
            let item =
                passwordItem[Math.floor(Math.random() * passwordItem.length)];
            password.push(item);
        } else {
            // If password large then 9, lastest 4 password will push in according to the random password index
            // Get the array values sequentially
            let newItem = passwordArray[n];
            let lastItem = newItem[Math.floor(Math.random() * newItem.length)];
            // Get array splice index
            let spliceIndex = Math.floor(Math.random() * password.length);
            password.splice(spliceIndex, 0, lastItem);
            n++;
        }
    }
    return password.join("");
}

import React, { useState, useEffect } from "react";
// import { QrReader } from "react-qr-reader";

interface ScanQRCodeProps {
    setCodeResult: (param: any) => void;
}

const ScanQRCode: React.FC<ScanQRCodeProps> = ({ setCodeResult }) => {
    const [result, setResult] = useState("");

    //   const handleScan = (data:any) => {
    //     if (data) {
    //         console.log(data.text,'扫描结果')
    //       setResult(data.text);
    //       // 这里可以添加发送数据到后端的逻辑
    //     }
    //   };
    const handleScan = (
        result?: any | undefined | null,
        error?: any | undefined | null,
    ) => {
        if (result !== null && result !== undefined) {
            // 处理扫描结果，result 是 string 类型
            console.log(result.text, "扫描结果");
            setResult(result.text);
            setCodeResult(result.text);
        } else {
            // 处理没有扫描到结果或未定义的情况
            console.log("没有扫描到结果或未定义");
        }
    };

    return (
        <div style={{ width: "100%", height: "100%" }}>
            {/* <h2>扫描二维码：</h2>   */}
            {/* <QrReader
        delay={300}
        onError={handleError}
        onScan={handleScan}
        style={{ width: '100%' }}
      />   */}
            {/* <QrReader
                constraints={{
                    facingMode: "environment",
                }}
                scanDelay={300}
                onResult={handleScan}
                videoContainerStyle={{ paddingTop: "0px", height: "100vh" }}
                videoStyle={{ width: "143%", height: "100%" }}
            /> */}
            {result && <p>扫描结果：{result}</p>}
        </div>
    );
};

export default ScanQRCode;

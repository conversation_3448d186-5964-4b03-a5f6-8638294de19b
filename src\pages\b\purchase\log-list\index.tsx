import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
    Button,
    Input,
    <PERSON><PERSON><PERSON>,
    Picker,
    InfiniteScroll,
    Toast,
    <PERSON>rror<PERSON>lock,
    DotLoading,
    PullToRefresh,
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import { useRequest, useInfiniteScroll, useDebounce } from "ahooks";
import useUrlState from "@ahooksjs/use-url-state";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";

import { INFINITE_LIST_PAGE_SIZE } from "@/config";
import { bSideRequests } from "@/services";

import styles from "./index.module.less";

export default () => {
    const navigate = useNavigate();
    /**
     *
     * 路由返回
     */

    const [urlState, setUrlState] = useUrlState<{
        listType: "normal" | "deprecated";
    }>(
        {
            listType: "normal",
        },
        {
            navigateMode: "replace",
        },
    );

    const getListRequest = useInfiniteScroll(
        (d) => {
            const page = d
                ? Math.ceil(d.list.length / INFINITE_LIST_PAGE_SIZE) + 1
                : 1;
            const stateMap = {
                normal: 0,
                deprecated: 1,
            } as any;
            return bSideRequests
                .getProducList({
                    pageIndex: page,
                    pageSize: INFINITE_LIST_PAGE_SIZE,
                    state: stateMap[urlState.listType],
                })
                .then((res) => {
                    const listInfo = res?.data?.data || {};
                    return {
                        list: listInfo?.records || [],
                        total: listInfo?.total || 0,
                    };
                });
        },
        {
            reloadDeps: [urlState.listType],
        },
    );
    const hasMore =
        getListRequest.data &&
        getListRequest.data.list.length < getListRequest.data.total;

    const data = getListRequest?.data?.list || [];

    const renderList = () => {
        if (getListRequest.loading) {
            return (
                <div
                    style={{
                        paddingTop: "30px",
                        textAlign: "center",
                        color: "var(--adm-color-weak)",
                    }}
                >
                    <div style={{ fontSize: 24, marginBottom: 24 }}>
                        <DotLoading />
                    </div>
                    正在加载数据
                </div>
            );
        }
        if (data.length === 0) {
            return (
                <ErrorBlock
                    image={<div></div>}
                    title="暂无数据"
                    description=""
                />
            );
        }
        return (
            <div>
                <PullToRefresh
                    onRefresh={async () => {
                        await getListRequest.reloadAsync();
                    }}
                >
                    <List className="packLogList">
                        {data.map((item) => {
                            return (
                                <List.Item
                                    key={item?.id}
                                    onClick={() => {
                                        sessionStorage.setItem(
                                            "productionId",
                                            item?.productionId,
                                        );
                                        navigate(item?.id?.toString());
                                    }}
                                >
                                    <div className="packLogList__item">
                                        <div className="packLogList__item--title">
                                            {item.purchaseBatch || "-"}
                                        </div>
                                        <div className="packLogList__item--info">
                                            <div>
                                                {item.purchaseTime
                                                    ? dayjs(
                                                          item.purchaseTime,
                                                      ).format(
                                                          "YYYY-MM-DD HH:mm:ss",
                                                      )
                                                    : "-"}
                                            </div>
                                        </div>
                                        <div className="packLogList__item--info">
                                            <div className="packLogList__item--info--text">
                                                <div className="packLogList__item--info--text--title">
                                                    收购人
                                                </div>
                                                <div className="packLogList__item--info--text--center">
                                                    {item.userName || "-"}
                                                </div>
                                            </div>
                                            <div className="packLogList__item--info--text">
                                                <div className="packLogList__item--info--text--title">
                                                    农户姓名
                                                </div>
                                                <div className="packLogList__item--info--text--center">
                                                    {item.farmerName || "-"}
                                                </div>
                                            </div>
                                            <div className="packLogList__item--info--text">
                                                <div className="packLogList__item--info--text--title">
                                                    收购重量(吨)
                                                </div>
                                                <div className="packLogList__item--info--text--center">
                                                    {item.purchaseWeight || "-"}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </List.Item>
                            );
                        })}
                    </List>
                </PullToRefresh>
                <InfiniteScroll
                    loadMore={async () => {
                        await getListRequest.loadMoreAsync();
                    }}
                    hasMore={hasMore || false}
                />
            </div>
        );
    };

    return (
        <div className={`${styles.logListBox}`}>
            <div className={`${styles.logList}`}>
                <CapsuleTabs
                    activeKey={urlState.listType}
                    onChange={(key: any) => {
                        setUrlState({
                            listType: key,
                        });
                    }}
                >
                    <CapsuleTabs.Tab
                        title="正常"
                        key="normal"
                    ></CapsuleTabs.Tab>
                    <CapsuleTabs.Tab
                        className="disabled"
                        title="已禁用"
                        key="deprecated"
                    ></CapsuleTabs.Tab>
                </CapsuleTabs>
                {renderList()}
            </div>
            <div className={`${styles.BtnBack}`}>
                <Button
                    className={`${styles.Btn}`}
                    block
                    // style={{ marginTop: 30 }}
                    fill="none"
                    color="primary"
                    onClick={() => {
                        navigate("/b/home/<USER>/newList");
                    }}
                >
                    新增
                </Button>
            </div>
        </div>
    );
};

import { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Button,
  Input,
  TextArea,
  Picker,
  Toast,
  Image,
  SpinLoading,
} from "antd-mobile";
import {
  PhoneFill,
  LockFill,
  EyeInvisibleOutline,
  EyeOutline,
} from "antd-mobile-icons";
import { useRequest } from "ahooks";
import request from "@/services";
import { bSideRequests } from "@/services";
import { rsaEncrypt } from "@/utils";
import useStore from "@/store";
import NavBar from "@/components/nav-bar";
import Form from "@/components/form";
import iconPassword from "@/assets/imgs/b-login-icon-password.png";

import logoNew from '@/assets/imgs/logonew.png';

import styles from "./index.module.less";
import { log } from "console";

export default () => {
  const navigate = useNavigate();
  const login = useStore((state) => state.login);

  const [visible, setVisible] = useState(false);
  const [entvisible, setentVisible] = useState(false);

  const [loginForm] = Form.useForm();

  const initJson: any = {
    title1: '欢迎使用',
    title2: '稻香汤原溯源平台',
    title3: '稻香汤原溯源平台',
  }
  const [titleJson, setTitleJson] = useState(initJson)

  const back = () => {
    navigate("/b/login");
    sessionStorage.clear();
    localStorage.clear();
  }

  const formValChange = () => {
    console.log(loginForm);

    let formInfo = loginForm.getFieldsValue()
    // console.log('触发', formInfo)
    if (loginForm && formInfo.password && formInfo.enterpassword) {
      loginForm.validateFields()
    }
  }

  return (
    <div className={`_global_page ${styles.login}`}>
      <div className='edit_pass'>
        <NavBar onBack={back}>修改密码</NavBar>
      </div>
      <div className="main login_input--style">
        <div className={styles.leftImgFont}>
          {/* <p className="pLine"></p> */}
          <p>{titleJson.title1}</p>
          <p>{titleJson.title2}</p>
        </div>
        <div className="title">
          {titleJson.title3}
          <img className={styles.titleIcon} src={logoNew} alt="" />
        </div>
        <Form
          form={loginForm}
          layout="horizontal"
          onValuesChange={formValChange}
          onFinishFailed={(failInfo) => {
            console.log(failInfo);

            const errorMsg =
              failInfo?.errorFields?.[0]?.errors?.[0];
            Toast.show(errorMsg);
          }}
          // onFinish={ok}
          onFinish={async (values) => {
            console.log(values);
            const pKRet = await request({
              method: "get",
              url: "/sys-config/getPublicKey",
              data: {},
            });
            const pK = pKRet?.data?.data || "";
            const valuedata = {
              userId: sessionStorage.getItem('userId'),
              password: await rsaEncrypt(values.password, pK)
            }
            console.log(valuedata);
            bSideRequests.firstReset(valuedata).then(res => {
              if (res.data.code === 200) {
                Toast.show(res.data.data)
                sessionStorage.clear();
                localStorage.clear();
                navigate("/b/login");

              }
            })

          }}
        // onFinish={async (values) => {
        //   console.log(values);
        //   const pKRet = await request({
        //     method: "get",
        //     url: "/sys-config/getPublicKey",
        //     data: {},
        //   });

        // }}
        >
          <div className="formArea ">
            <div className="formItem">
              <Image
                className="formItem__label"
                src={iconPassword}
              ></Image>
              <Form.Item
                name="password"
                noStyle
                rules={[
                  {
                    required: true,
                    message: '请填写密码'
                  },
                  () => ({
                    validator: (_, value, callback) => {
                      const regExp = new RegExp(
                        /^(?=.*[A-Z].*)(?=.*[a-z].*)(?=.*\d)(?=.*[!#$%])[A-Za-z\d!#$%]{8,20}$/
                      );
                      const verify = regExp.test(value);
                      if (!value) {
                        callback('请填写密码');
                      } else if (verify === false) {
                        callback('新密码不符合密码规则！');
                      } else {
                        callback();
                      }
                    }
                  })
                ]}
              >
                <Input
                  className="formItem__input"
                  type={visible ? "text" : "password"}
                  placeholder="新密码"
                />

              </Form.Item>
              <div className="eye">
                {!visible ? (
                  <EyeInvisibleOutline
                    onClick={() => setVisible(true)}
                  />
                ) : (
                  <EyeOutline
                    onClick={() => setVisible(false)}
                  />
                )}
              </div>
              <Form.Item noStyle shouldUpdate>
                {() => {
                  const { errors = [] } =
                    loginForm
                      .getFieldsError()
                      .find((item) => {
                        return (
                          item.name[0] === "password"
                        );
                      }) || {};
                  return (
                    errors.length > 0 && (
                      <div className="errInfo">
                        <div className="formItem__label"></div>
                        {errors[0]}
                      </div>
                    )
                  );
                }}
              </Form.Item>
            </div>
            <div className="formItem">
              <Image
                className="formItem__label"
                src={iconPassword}
              ></Image>
              <Form.Item
                name="enterpassword"
                noStyle
                rules={[
                  {
                    required: true,
                    message: '请再次输入密码'
                  },
                  ({ getFieldValue }) => ({
                    validator(rule, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject('两次密码输入不一致！');
                    }
                  })
                ]}
              >
                <Input
                  className="formItem__input"
                  type={entvisible ? "text" : "password"}
                  placeholder="确认密码"
                />
              </Form.Item>
              <div className="eye">
                {!entvisible ? (
                  <EyeInvisibleOutline
                    onClick={() => setentVisible(true)}
                  />
                ) : (
                  <EyeOutline
                    onClick={() => setentVisible(false)}
                  />
                )}
              </div>
              <Form.Item noStyle shouldUpdate>
                {() => {
                  const { errors = [] } =
                    loginForm
                      .getFieldsError()
                      .find((item) => {
                        return (
                          item.name[0] === "enterpassword"
                        );
                      }) || {};
                  return (
                    errors.length > 0 && (
                      <div className="errInfo">
                        <div className="formItem__label"></div>
                        {errors[0]}
                      </div>
                    )
                  );
                }}
              </Form.Item>
            </div>
          </div>
          <Button
            className="loginBtn"
            // loading={loginRequest.loading}
            block
            fill='none'
            type="submit"
            color="primary"
          >
            确定
          </Button>
          <div className="rule">
            <p>密码规则：</p>
            <p>1.长度为8-20位</p>
            <p>2.必须包含数字0-9</p>
            <p>3.必须包含一位大写字母和一位小写字母</p>
            <p>4.必须至少包含特殊字符!#$%中的一个</p>
          </div>
        </Form>
      </div>
    </div>
  );
};

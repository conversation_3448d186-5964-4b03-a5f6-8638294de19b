import React, { useEffect, useState } from "react";
import { TabBar as AntdTabBar } from "antd-mobile";
import { LeftOutline } from "antd-mobile-icons";
import { useTitle } from "ahooks";
import {
    create<PERSON>rowserRouter,
    RouterProvider,
    Navigate,
    Outlet,
    useOutlet,
    useOutletContext,
    useLocation,
    useNavigate,
} from "react-router-dom";

import type { TabBarProps } from "antd-mobile";

import styles from "./index.module.less";

const TabBar = (props: TabBarProps) => {
    const navigate = useNavigate();

    return (
        <div className={styles.tabBar}>
            <AntdTabBar {...props}></AntdTabBar>
        </div>
    );
};

TabBar.Item = AntdTabBar.Item;

export default TabBar;

export const RouteTabBar = ({ tabs, prefix = "" }: any) => {
    let location = useLocation();
    const navigate = useNavigate();
    const [tabBarActiveKey, setTabBarActiveKey] = useState(tabs[0].key);
    useEffect(() => {
        const curPath = location.pathname.replace(prefix + "/", "");
        setTabBarActiveKey(curPath.split("/")[0]);
    }, [location.pathname]);
    return (
        <TabBar
            className={styles.routeTabBar}
            activeKey={tabBarActiveKey}
            onChange={(key) => {
                navigate(`${prefix}/${key}`, {
                    replace: true,
                });
                // setTabBarActiveKey(key)
            }}
        >
            {tabs.map((item: any) => (
                <TabBar.Item
                    key={item.key}
                    icon={item.icon}
                    title={item.title}
                />
            ))}
        </TabBar>
    );
};

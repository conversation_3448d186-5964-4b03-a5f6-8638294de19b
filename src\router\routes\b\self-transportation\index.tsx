import React, { useEffect, useState } from "react";
import {
    create<PERSON><PERSON><PERSON><PERSON><PERSON>er,
    RouterProvider,
    Navigate,
    Outlet,
    useOutlet,
    useOutletContext,
    useLocation,
    useNavigate,
    Link,
} from "react-router-dom";
import { Badge } from "antd-mobile";

import NavBar from "@/components/nav-bar";
import TabBar, { RouteTabBar } from "@/components/tab-bar";
import CacheRouteContainer from "@/components/cache-route-container";

import type { RouteObject } from "react-router-dom";

import iconTransportation from "@/assets/imgs/tabs/transportation.png";
import iconTransportationSelected from "@/assets/imgs/tabs/transportation-selected.png";
import iconLog from "@/assets/imgs/tabs/log.png";
import iconLogSelected from "@/assets/imgs/tabs/log-selected.png";

import { ReactComponent as LogIcon } from "@/assets/imgs/tabs/svg/log.svg";
import { ReactComponent as TransportationIcon } from "@/assets/imgs/tabs/svg/transportation.svg";

import styles from "./index.module.less";

import BSelfTransportationEntry from "@/pages/b/self-transportation/self-transportation-entry";
import SelfTransportationLogDetail from "@/pages/b/self-transportation/self-transportation-log-detail";
import SelfTransportationLogList from "@/pages/b/self-transportation/self-transportation-log-list";
import BReportErrorResult from "@/pages/b/report-error-result";
import NoPermissionPage from "@/pages/403";

import useStore from "@/store";

const PackLayout = () => {
    const [pageTitle, setPageTitle] = useState("");
    const userMenuPermission = useStore((state) => state.userMenuPermission);

    const tabs = [
        {
            key: "entry",
            title: "物流录入",
            icon: (active: boolean) => (
                // active ? <img src={iconTransportationSelected} /> : <img src={iconTransportation} />,
                <TransportationIcon
                    style={{
                        color: active ? "var(--primary-color)" : "#e7e7e7",
                    }}
                ></TransportationIcon>
            ),
        },
        {
            key: "log",
            title: "记录查看",
            icon: (active: boolean) => (
                // active ? <img src={iconLogSelected} /> : <img src={iconLog} />,
                <LogIcon
                    style={{
                        color: active ? "var(--primary-color)" : "#e7e7e7",
                    }}
                ></LogIcon>
            ),
        },
    ];

    if (!userMenuPermission.find((item) => item.perms === "logistics")) {
        return (
            <div className={`_global_page ${styles.layout}`}>
                <NavBar>{pageTitle}</NavBar>
                <NoPermissionPage></NoPermissionPage>
            </div>
        );
    }

    return (
        <div className={`_global_page ${styles.layout}`}>
            <NavBar>{pageTitle}</NavBar>
            <div className="_global_pageScrollContent">
                <Outlet context={{ setPageTitle }}></Outlet>
            </div>
            <RouteTabBar
                tabs={tabs}
                prefix="/b/home/<USER>"
            ></RouteTabBar>
        </div>
    );
};

interface IPackPageWrapperProps {
    title?: string;
    children: React.ReactNode;
}
const PackPageWrapper = ({ children, title = "" }: IPackPageWrapperProps) => {
    const { setPageTitle } = useOutletContext<any>();
    useEffect(() => {
        setPageTitle(title);
    }, [title]);

    return children;
};

export default {
    path: "self-transportation",
    element: <PackLayout></PackLayout>,
    children: [
        {
            index: true,
            element: <Navigate to="entry" replace></Navigate>,
        },
        {
            path: "entry",
            element: <CacheRouteContainer></CacheRouteContainer>,
            children: [
                {
                    index: true,
                    element: (
                        <PackPageWrapper title="物流录入">
                            <BSelfTransportationEntry></BSelfTransportationEntry>
                        </PackPageWrapper>
                    ),
                },
                {
                    path: "result",
                    element: (
                        <PackPageWrapper title="物流录入">
                            <BReportErrorResult></BReportErrorResult>
                        </PackPageWrapper>
                    ),
                },
            ],
        },
        {
            path: "log",
            children: [
                {
                    index: true,
                    element: (
                        <PackPageWrapper title="运输单列表">
                            <SelfTransportationLogList></SelfTransportationLogList>
                        </PackPageWrapper>
                    ),
                },
                {
                    path: ":id",
                    element: (
                        <PackPageWrapper title="运输单详情">
                            <SelfTransportationLogDetail></SelfTransportationLogDetail>
                        </PackPageWrapper>
                    ),
                },
            ],
        },
    ],
} as RouteObject;

import { useState, useCallback, useRef, useEffect, memo } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
    Button,
    Input,
    TextArea,
    Picker,
    Space,
    Popup,
    CheckList,
    Toast,
    Stepper,
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
    UpOutline,
    DownOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import useUrlState from "@ahooksjs/use-url-state";
import { useImmer } from "use-immer";
import { useRequest, useSetState } from "ahooks";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";
import CollapseTable from "@/components/collapse-table";
import CheckListPopup from "@/components/check-list-popup";

import iconScanQrCode from "@/assets/imgs/scan-qrcode.png";
import { ReactComponent as SvgScanQrCode } from "@/assets/imgs/scan-qrcode.svg";

import {
    REPOSITORY_TYPE_CONSTANTS,
    MIN_SCAN_LENGTH,
    BOX_CODE_STATE_ERR,
} from "@/config";
import { useConfirmBlock, useScanQRCode } from "@/hooks";
import {
    bSideRequests,
    getPackCodeInfoByPackCode,
    getPackCodeInfoByPackCodeList,
    BoxCodeVerifyModule,
} from "@/services";
import { showToastLoading, showConfirmModal, requestDataSign } from "@/utils";

import usePropsImmerValue from "./use-props-immer-value";

import styles from "./index.module.less";

export type BoxCodeData = {
    id: number | string;
    productName: any;
    inboundNum: any;
    orgName: any;
    productionBatch: any;

    // code: any;
    // name: any;
}[];

interface BoxCodeScannerProps {
    value?: BoxCodeData;
    defaultValue?: BoxCodeData;
    onChange?: (data: BoxCodeData) => void;
    onQuantityChange?: (quantity: number) => void;
    module: BoxCodeVerifyModule;
}

const BoxCodeScanner = (props: BoxCodeScannerProps) => {
    const {
        onChange,
        value,
        defaultValue = [],
        module,
        onQuantityChange,
    } = props;
    const { qrCodeScanElement, startScanQrCode, closeScanQrCode } =
        useScanQRCode();
    const [inboundInfoForm] = Form.useForm();

    const [scannedBoxCodeInfo, setScannedBoxCodeInfo] =
        usePropsImmerValue<BoxCodeData>({ onChange, value, defaultValue });
    const scannedBoxCodeInfoRef = useRef(scannedBoxCodeInfo);
    scannedBoxCodeInfoRef.current = scannedBoxCodeInfo;

    // 添加入库数量状态
    const [inboundQuantity, setInboundQuantity] = useState<number>(1);
    const getScannedBoxCodeInfo = useCallback(
        () => scannedBoxCodeInfoRef.current,
        [],
    );

    const getPackCodeInfoByPackCodeRequest = useRequest(
        getPackCodeInfoByPackCodeList,
        {
            manual: true,
        },
    );

    const handleBoxCode = async (
        boxCodeStr: string,
        continueScan: () => void,
    ) => {
        const { close: closeLoading } = Toast.show({
            icon: "loading",
        });
        const continueScanWithMessage = (
            content: React.ReactNode,
            icon?: string,
        ) => {
            closeLoading();
            Toast.show({
                icon: icon,
                content: content,
                duration: 1000,
                afterClose() {
                    continueScan();
                },
            });
        };
        try {
            console.log(scannedBoxCodeInfo, "scannedBoxCodeInfo");
            // setScannedBoxCodeInfo((draft) => {
            //     draft.push({
            //         id: 1,
            //         name: "123123",
            //         code: "code",
            //     });
            //     // 在这里打印更新后的数据
            //     console.log([...draft], "更新后的scannedBoxCodeInfo");
            // });

            // Check if we're scanning a product traceability code
            const isFromQRScan = continueScan !== undefined;

            const boxInfoRet = await getPackCodeInfoByPackCodeRequest.runAsync({
                id: boxCodeStr,
                type: isFromQRScan ? 2 : undefined, // Add type=2 when scanning product traceability codes
            });

            const boxInfo = boxInfoRet?.data?.data || {};

            console.log(boxInfo, "boxInfo");

            if (boxInfo?.id) {
                // const existIds = getScannedBoxCodeInfo().map((item) => item.id);
                //   if (existIds.includes(boxInfo.id)) {
                //     continueScanWithMessage("该溯源码已添加");
                // }
                if (boxInfo.basicInfoTo?.status == 0) {
                    continueScanWithMessage(
                        "该批次产品已成功入库，请勿重复操作",
                    );
                } else {
                    setScannedBoxCodeInfo((draft) => {
                        draft.push({
                            id: boxInfo.id,
                            productName: boxInfo.product?.productName || "-",
                            orgName: boxInfo.basicInfoTo?.orgName || "-",
                            productionBatch:
                                boxInfo.basicInfoTo?.productionBatch || "-",
                            inboundNum: boxInfo.basicInfoTo?.amount || 1,
                            // basicInfoTo
                            // code: boxInfo.code,
                            // code: boxInfo.code,
                            // code: boxInfo.code,
                        });
                    });

                    // 设置入库数量的初始值
                    const initialQuantity = boxInfo.basicInfoTo?.amount || 1;
                    setInboundQuantity(initialQuantity);
                    onQuantityChange?.(initialQuantity);

                    continueScanWithMessage(
                        <div>
                            {/* <div>{boxInfo.code}</div> */}
                            <div>扫描成功</div>
                        </div>,
                    );
                    console.log(getScannedBoxCodeInfo().length);

                    if (getScannedBoxCodeInfo().length + 1 >= MIN_SCAN_LENGTH) {
                        closeScanQrCode();
                    }
                }
            } else {
                continueScanWithMessage("获取溯源码信息失败");
            }
        } catch (err: any) {
            console.log(err, "err");
            if (err?.type === BOX_CODE_STATE_ERR) {
                continueScanWithMessage(
                    err?.message || "解析溯源码失败",
                    "fail",
                );
            } else {
                const errMsg = err?.response?.data?.message;
                continueScanWithMessage(errMsg || "解析溯源码失败", "fail");
            }
        }
    };

    return (
        <div className={styles.boxCodeScanner}>
            {scannedBoxCodeInfo.length > 0 ? (
                <div>
                    <div className={styles.scanFrom}>
                        <div>生产加工企业</div>
                        <div>{scannedBoxCodeInfo[0].orgName}</div>
                    </div>
                    <div className={styles.scanFrom}>
                        <div>产品名称</div>
                        <div>{scannedBoxCodeInfo[0].productName}</div>
                    </div>
                    <div className={styles.scanFrom}>
                        <div>生产批次</div>
                        <div>
                            {(() => {
                                const batch =
                                    scannedBoxCodeInfo[0].productionBatch ||
                                    "-";
                                return batch.length > 16
                                    ? batch.substring(0, 16) + "..."
                                    : batch;
                            })()}
                        </div>
                    </div>
                    <div className={styles.scanFrom}>
                        <div>入库数量</div>
                        <div>
                            {" "}
                            <Stepper
                                value={inboundQuantity}
                                min={1}
                                max={9999999999}
                                step={1}
                                digits={0}
                                style={{ width: "160px" }}
                                // parser={(value: any) => {
                                //     // 移除所有非数字字符，只保留数字
                                //     let cleaned = value.replace(/[^\d]/g, "");
                                //     // 转换为数字并向下取整
                                //     let numValue = parseInt(cleaned) || 0;
                                //     // 确保为大于0的正整数
                                //     numValue = Math.max(1, numValue);
                                //     return numValue.toString();
                                // }}
                                onChange={(value: string | number) => {
                                    // 确保值为大于0的正整数
                                    const intValue =
                                        parseInt(value.toString()) || 1;
                                    console.log(
                                        "Stepper onChange - 原始值:",
                                        value,
                                        "转换后:",
                                        intValue,
                                    );
                                    // 更新状态，实现回显
                                    setInboundQuantity(intValue);
                                    // 通知父组件数量变化
                                    onQuantityChange?.(intValue);
                                    console.log(
                                        "已通知父组件数量变化:",
                                        intValue,
                                    );
                                }}
                            />
                        </div>
                    </div>
                    <div className={styles.warmTip}>
                        温馨提示：默认入库数量为批次加工数量，可根据实际情况进行修改
                    </div>
                </div>
            ) : (
                // <Form
                //     form={inboundInfoForm}
                //     layout="horizontal"
                //     className={styles.scanFrom}
                // >
                //     <Form.Item name="productId" label="仓库名称">
                //         <Input placeholder="点击输入仓储地点" />
                //     </Form.Item>
                //     <Form.Item name="productId" label="仓库名称">
                //         <Input placeholder="点击输入仓储地点" />
                //     </Form.Item>
                //     <Form.Item name="productId" label="仓库名称">
                //         <Input placeholder="点击输入仓储地点" />
                //     </Form.Item>
                //     <Form.Item name="productId" label="仓库名称">
                //         <Input placeholder="点击输入仓储地点" />
                //     </Form.Item>
                // </Form>
                ""
            )}

            {scannedBoxCodeInfo.length < MIN_SCAN_LENGTH && (
                <Button
                    loading={getPackCodeInfoByPackCodeRequest.loading}
                    className={styles.scanBtn}
                    color="primary"
                    fill="outline"
                    onClick={async () => {
                        startScanQrCode((boxCodeStr, continueScan) => {
                            console.log(boxCodeStr, "boxCodeStr");
                            const url = boxCodeStr;
                            const params = new URLSearchParams(
                                new URL(url).search,
                            );
                            const traceCodeId = params.get("traceCodeId");
                            console.log(traceCodeId, "traceCodeId");

                            if (traceCodeId) {
                                handleBoxCode(traceCodeId, continueScan);
                            }
                        });
                    }}
                >
                    <SvgScanQrCode
                        className={styles.scanBtnIcon}
                    ></SvgScanQrCode>
                    <div>扫描产品溯源码</div>
                </Button>
            )}
            {qrCodeScanElement}
        </div>
    );
};

export default memo(BoxCodeScanner);

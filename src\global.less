//out:false
html,
body {
    width: 100%;
    height: 100%;
}

#root {
    transform: translateZ(0);
    height: 100%;
    position: relative;
}

._global_page {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: fff;
}

._global_pageScrollContent {
    flex: 1;
    overflow: auto;
    /* 隐藏滚动条 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 和 Edge */
}

._global_pageScrollContent::-webkit-scrollbar {
    display: none; /* Chrome、Safari 和 Opera */
}

.adm-image-viewer-slides {
    overflow: hidden;
}

.wrap_input--style {
    .adm-list-item {
        .adm-list-item-content {
            box-shadow: inset 0 0 10px #e8efdc;
            border: 1px solid #8eb249;
            border-radius: 8px;
            height: 30px;

            .adm-list-item-content-main {
                padding: 0;
            }
        }
    }
}

.login_input--style {
    .formItem {
        box-shadow: inset 0 0 10px #e8efdc;
        border: 1px solid #8eb249;
        border-radius: 8px;
        height: 30px;

        &:first-child {
            margin-top: 5px;
        }
    }
}

.adm-button::before {
    background-color: transparent;
}

.block {
    display: block;
}

.none {
    display: none;
}

.imgbox {
    width: 100%;
    height: 100%;

    .adm-image {
        width: 100%;
        height: 100vh;
    }
}

.trace_btn {
    margin: 0 auto;
    cursor: pointer;
    width: 88%;
    height: 60px;
    display: flex;
    justify-content: center;
    color: #fff;
    //  border: 1px solid;
    // background: linear-gradient(to bottom, #FFDF5F, #E27D0A);
    border-radius: 10px;
    background-image: url("@/assets/imgs/bimgs/btn1.png");
    background-size: cover;
    background-position: 100% 18%;
    position: absolute;
    bottom: 40px;
    left: 23px;

    // background:
    //     url("@/assets/imgs/bimgs/btn.png")no-repeat center/100% 100%,
    //     linear-gradient(180deg, #ffff 0%, #ffff 100%);
    span {
        text-align: center;
        color: white;
    }
}
.trace_btnc {
    margin: 0 auto;
    cursor: pointer;
    width: 88%;
    height: 60px;
    display: flex;
    justify-content: center;
    color: #fff;
    //  border: 1px solid;
    // background: linear-gradient(to bottom, #FFDF5F, #E27D0A);
    border-radius: 10px;
    background-image: url("@/assets/imgs/bimgs/btn1c.png");
    background-size: cover;
    background-position: 100% 18%;
    position: absolute;
    bottom: 40px;
    left: 23px;

    // background:
    //     url("@/assets/imgs/bimgs/btn.png")no-repeat center/100% 100%,
    //     linear-gradient(180deg, #ffff 0%, #ffff 100%);
    span {
        text-align: center;
        color: white;
    }
}
.tolink {
    width: 260px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    background: #fdeec9;
    text-decoration: none;
    border-radius: 9px;
    margin: 0 auto;
    border: 1px solid;
}
.eye_style {
    display: flex;
    position: relative;

    .eye {
        position: absolute;
        right: 16px;
        top: 7px;
    }
}
.adm-form-item-child-inner {
    text-align: right;
}
._scanInbound_1suaq_1 {
    .adm-form-item-child-inner {
        text-align: left !important;
    }
}
.custom-modal-body {
    .adm-modal-title {
        height: 50px;
        line-height: 50px;
        color: #fff;
        border-radius: 8px 8px 0px 0px;
        background: #76ae55;
        font-family: PingFang SC;
        font-size: 18px;
        font-weight: 500;
        text-align: left;
    }

    .content {
        margin-top: 8px;
    }
    .title,
    .desc {
        font-family: PingFang SC;
        font-size: 14px;
        font-weight: normal;
        line-height: 20px;
        letter-spacing: 0px;
        color: #3d3d3d;
    }
    .desc {
        margin-top: 20px;
    }
    .action {
        text-align: right;
        margin-top: 20px;
        .cancel-btn,
        .ok-btn {
            display: inline-block;
            width: 64px;
            height: 32px;
            border-radius: 2px;
            background: #969696;
            color: #fff;
            font-family: PingFang SC;
            font-size: 16px;
            font-weight: normal;
            line-height: 32px;
            text-align: center;
        }
        .ok-btnC {
            display: inline-block;
            width: 84px;
            height: 32px;
            border-radius: 2px;
            background: #969696;
            color: #fff;
            font-family: PingFang SC;
            font-size: 16px;
            font-weight: normal;
            line-height: 32px;
            text-align: center;
            background: #80a932;
            margin-left: 10px;
        }
        .ok-btn {
            background: #80a932;
            margin-left: 10px;
        }
    }
}

.custom-modal-body {
    padding-top: 0 !important;
}
.adm-center-popup-close {
    color: #fff;
}
.adm-space-item {
    color: red !important;
}
.adm-form .adm-form-footer {
    padding: 0 8px !important;
}
._ratingPopup_i46hm_221 {
    .adm-list-item-content {
        border-top: none !important;
    }
}
.adm-list-default .adm-list-body {
    border-top: none !important;
    border-bottom: none !important;
}
.adm-list-item-content {
    border-top: none !important;
}
.adm-number-keyboard-popup.adm-popup .adm-popup-body {
    width: 380px !important;
    // max-width: 100% !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    border-radius: 8px !important;
}
._form_16a7b_1 {
    .adm-form-item-child-inner {
        text-align: left;
    }
}
.adm-passcode-input-cell {
    background: #fdeec9;
}
.adm-passcode-input.adm-passcode-input-seperated .adm-passcode-input-cell {
    border: none;
}

import React, { useEffect, useState } from "react";
import {
    create<PERSON><PERSON><PERSON><PERSON><PERSON>er,
    RouterProvider,
    Navigate,
    Outlet,
    useOutlet,
    useOutletContext,
    useLocation,
    useNavigate,
    Link,
    useMatches,
    useNavigationType,
} from "react-router-dom";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd-mobile";

import NavBar from "@/components/nav-bar";
import TabBar, { RouteTabBar } from "@/components/tab-bar";
import CacheRouteContainer from "@/components/cache-route-container";

import type { RouteObject } from "react-router-dom";

import iconInbound from "@/assets/imgs/tabs/inbound.png";
import iconInboundSelected from "@/assets/imgs/tabs/inbound-selected.png";
import iconLog from "@/assets/imgs/tabs/log.png";
import iconLogSelected from "@/assets/imgs/tabs/log-selected.png";

import { ReactComponent as LogIcon } from "@/assets/imgs/tabs/svg/jiLu.svg";
import { ReactComponent as ProductiveIcon } from "@/assets/imgs/tabs/svg/guoCheng.svg";

import styles from "./index.module.less";

// import BProductiveProcessEntry from "@/pages/b/productive-process/entry";
// import BProductiveProcessLogList from "@/pages/b/productive-process/log-list";
// import BProductiveProcessLogDetail from "@/pages/b/productive-process/log-detail";

import BPurchaseEntry from "@/pages/b/purchase/entry";
import BPurchaseLogList from "@/pages/b/purchase/log-list";
import BPurchaseLogDetail from "@/pages/b/purchase/editList";

import BPurchaseNewList from "@/pages/b/purchase/newList";
import NoPermissionPage from "@/pages/403";

import useStore from "@/store";

const PackLayout = () => {
    const [pageTitle, setPageTitle] = useState("");
    const [editPageState, setEditPageState] = useState<{
        disabled: boolean;
        id: string;
    } | null>(null);
    const userMenuPermission = useStore((state) => state.userMenuPermission);
    const navigate = useNavigate();

    const tabs = [
        {
            key: "entry",
            title: "过程录入",
            icon: (active: boolean) => (
                // active ? <img src={iconInboundSelected} /> : <img src={iconInbound} />,
                <ProductiveIcon
                    style={{
                        color: active ? "#347934" : "#e7e7e7",
                    }}
                ></ProductiveIcon>
            ),
        },
        {
            key: "log",
            title: "记录查看",
            icon: (active: boolean) => (
                // active ? <img src={iconLogSelected} /> : <img src={iconLog} />,
                <LogIcon
                    style={{
                        color: active ? "#347934" : "#e7e7e7",
                    }}
                ></LogIcon>
            ),
        },
    ];
    console.log(userMenuPermission);

    if (!userMenuPermission.find((item) => item.perms === "purchase")) {
        return (
            <div className={`_global_page ${styles.layout}`}>
                <NavBar>{pageTitle}</NavBar>
                <NoPermissionPage></NoPermissionPage>
            </div>
        );
    }

    type PageTitle = "收购列表" | "收购信息" | "新增收购";

    // 使用类型别名作为 routesMap 的键
    const routesMap: { [key in PageTitle]: string } = {
        收购列表: "/b/home",
        收购信息: "/b/home/<USER>/log",
        新增收购: "/b/home/<USER>/log",
    };

    // 定义 back 函数并使用 PageTitle 类型
    const back = (pageTitle: PageTitle) => {
        // 如果是收购信息页面（编辑页面）
        if (pageTitle === "收购信息" && editPageState) {
            if (editPageState.disabled) {
                // 当前是禁用状态（有编辑按钮），返回到收购列表
                console.log("有编辑按钮状态，返回到收购列表");
                navigate("/b/home/<USER>/log");
                return;
            } else {
                // 当前是编辑状态（没有编辑按钮），返回到禁用状态（有编辑按钮）
                console.log("没有编辑按钮状态，返回到有编辑按钮状态");
                // 触发页面回到禁用状态
                window.dispatchEvent(new CustomEvent("backToDisabled"));
                return;
            }
        }

        // 其他页面使用默认逻辑
        const path = routesMap[pageTitle];
        if (path) {
            navigate(path);
        } else {
            console.warn(`Unknown page title: ${pageTitle}`);
        }
    };
    return (
        <div className={`_global_page ${styles.layout}`}>
            {/* <NavBar onBack={pageTitle=='收购列表'?navigate("/b/personalinfo"):''}>{pageTitle}</NavBar> */}
            <NavBar onBack={() => back(pageTitle)}>{pageTitle}</NavBar>
            <div className="_global_pageScrollContent">
                <Outlet
                    context={{ setPageTitle, setEditPageState, editPageState }}
                ></Outlet>
            </div>
            {/* <RouteTabBar tabs={tabs} prefix="/b/home/<USER>"></RouteTabBar> */}
        </div>
    );
};

interface IPackPageWrapperProps {
    title?: string;
    children: React.ReactNode;
}
const PackPageWrapper = ({ children, title = "" }: IPackPageWrapperProps) => {
    const { setPageTitle } = useOutletContext<any>();
    console.log(title);
    // if()

    useEffect(() => {
        setPageTitle(title);
    }, [title]);

    return children;
};

export default {
    path: "purchase",
    element: <PackLayout></PackLayout>,
    children: [
        {
            index: true,
            element: <Navigate to="log" replace></Navigate>,
        },
        {
            path: "entry",
            handle: {
                canBack: true,
            },
            element: (
                <PackPageWrapper>
                    <BPurchaseEntry></BPurchaseEntry>
                </PackPageWrapper>
            ),
        },
        {
            path: "log",
            // element: <CacheRouteContainer shouldCache={(location: any) => {
            //     return location.pathname === "/b/home/<USER>/log"
            // }}></CacheRouteContainer>,
            handle: {
                canBack: true,
            },
            children: [
                {
                    index: true,
                    element: (
                        <PackPageWrapper title="收购列表">
                            <BPurchaseLogList></BPurchaseLogList>
                        </PackPageWrapper>
                    ),
                },
                {
                    path: ":id",
                    handle: {
                        canBack: true,
                    },
                    element: (
                        <PackPageWrapper title="收购信息">
                            <BPurchaseLogDetail></BPurchaseLogDetail>
                        </PackPageWrapper>
                    ),
                },
            ],
        },
        {
            path: "newList",
            handle: {
                canBack: true,
            },
            element: (
                <PackPageWrapper title="新增收购">
                    <BPurchaseNewList></BPurchaseNewList>
                </PackPageWrapper>
            ),
        },
    ],
} as RouteObject;

//out:false
.scanInbound {
    margin: 10px;
    padding: 20px 16px;
    padding-bottom: 30px;
    background: var(--page-block-color);
    border-radius: var(--border-radius);

    :global {

        // .adm-image-uploader-upload-button-wrap{
        //   background: #FDEEC9;
        //   color: #FFB200;
        // }
        .adm-text-area {
            background: #FFFAED;
        }
    }

    ._uploadBtn_g8r4v_4 {
      border: 1px solid #FFB200 !important;
      background: #FDEEC9 !important;
      color: #FFB200 !important;
  }
    .Btn {
        height: 60px;
        margin-top: 25px;
        // border-radius: 10px;
        // padding: 13px 12px 15px 12px;
        font-size: 16px;
        line-height: 19px;
        letter-spacing: 0px;
        background-image: url('@/assets/imgs/bimgs/btn1.png');
        background-size: 104% 106%;
        background-position: center 3px;
        background-repeat: no-repeat;
        // background-color: transparent;
        // background: red;
        border: 0;

        span {
            color: white;
        }
    }

}
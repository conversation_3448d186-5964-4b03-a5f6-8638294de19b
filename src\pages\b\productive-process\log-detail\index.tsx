import { useState } from "react";
import { useNavigate, useParams, useOutletContext } from "react-router-dom";
import {
    Button,
    Input,
    TextArea,
    Picker,
    Space,
    Image,
    DotLoading,
    Toast,
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
    UpOutline,
    DownOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import { useRequest, useInfiniteScroll, useDebounce } from "ahooks";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";
import CollapseTable from "@/components/collapse-table";

import iconScanQrCode from "@/assets/imgs/scan-qrcode.png";

import { showConfirmModal } from "@/utils";
import { bSideRequests } from "@/services";

import styles from "./index.module.less";
import { decryptedUrl, isArrayArr } from "@/utils/index";

export default () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const [promotionPicData, setPromotionPicData] = useState<any>();
    const [video, setVideo] = useState<any>();

    const getProductiveProcessDetailRequest = useRequest(
        () => {
            if (!id) {
                throw new Error("缺少id");
            }
            return bSideRequests.getProductiveProcessDetail(id);
        },
        {
            async onSuccess(res: any) {
                console.log("ssss===", res);
                const result = res?.data?.data;
                const videos = await decryptedUrl(result?.processVideo);
                const arrayData = await Promise.all(
                    isArrayArr(result?.processImg)?.map((item: any) => {
                        return decryptedUrl(item);
                    }),
                );
                // const image = await decryptedUrl(result?.productImg)
                setVideo(videos);
                setPromotionPicData(arrayData);
            },
            refreshDeps: [id],
        },
    );
    const productiveProcessDetail =
        getProductiveProcessDetailRequest.data?.data?.data || {};

    const deprecateProductiveProcessRequest = useRequest(
        () => {
            if (!id) {
                throw new Error("缺少id");
            }
            return bSideRequests.deprecateProductiveProcess(id);
        },
        {
            manual: true,
            onSuccess() {
                Toast.show({
                    icon: "success",
                    content: "作废成功",
                });
                getProductiveProcessDetailRequest.refresh();
            },
        },
    );

    if (getProductiveProcessDetailRequest.loading) {
        return (
            <div
                style={{
                    paddingTop: "50px",
                    textAlign: "center",
                    color: "var(--adm-color-weak)",
                }}
            >
                <div style={{ fontSize: 24, marginBottom: 24 }}>
                    <DotLoading />
                </div>
                正在加载数据
            </div>
        );
    }

    return (
        <div className={`${styles.LogDetail}`}>
            <List>
                <List.Item extra={productiveProcessDetail.processName || "-"}>
                    名称
                </List.Item>
                <List.Item
                    extra={
                        productiveProcessDetail.createTime
                            ? dayjs(productiveProcessDetail.createTime).format(
                                  "YYYY-MM-DD HH:mm:ss",
                              )
                            : "-"
                    }
                >
                    创建时间
                </List.Item>
            </List>
            <div className={styles.LogDetail__OutList}>
                {promotionPicData && promotionPicData.length > 0 && (
                    <div className={styles.infoShow}>
                        <div className={styles.infoShow__label}>
                            生产过程图片
                        </div>
                        {promotionPicData.map((img: string) => {
                            return <Image src={img}></Image>;
                        })}
                    </div>
                )}
                {productiveProcessDetail.processVideo && video && (
                    <div className={styles.infoShow}>
                        <div className={styles.infoShow__label}>
                            生产过程视频
                        </div>
                        <video
                            style={{
                                width: "100%",
                            }}
                            controls
                            playsInline
                            src={video}
                        ></video>
                    </div>
                )}
                {productiveProcessDetail.processInstructions && (
                    <div className={styles.infoShow}>
                        <div className={styles.infoShow__label}>
                            生产过程说明
                        </div>
                        <div
                            className={styles.infoShow__value}
                            style={{
                                whiteSpace: "pre-wrap",
                                wordBreak: "break-all",
                            }}
                        >
                            {productiveProcessDetail.processInstructions}
                        </div>
                    </div>
                )}
                {productiveProcessDetail.state === 0 && (
                    <Button
                        className={`${styles.Btn}`}
                        loading={deprecateProductiveProcessRequest.loading}
                        style={{ marginTop: 50 }}
                        shape="rounded"
                        block
                        fill="none"
                        color="primary"
                        type="submit"
                        onClick={() => {
                            showConfirmModal({
                                title: "确认作废",
                                content: "确认作废吗",
                                confirmBtnProps: {
                                    color: "danger",
                                },
                                onConfirm() {
                                    deprecateProductiveProcessRequest.run();
                                },
                            });
                        }}
                    >
                        作废
                    </Button>
                )}
            </div>
        </div>
    );
};

import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import fs from "fs";
import postcsspxtoviewport from "postcss-px-to-viewport";
import mobile from 'postcss-mobile-forever' // <---- 这里
import svgr from "vite-plugin-svgr";
import { nodePolyfills } from "vite-plugin-node-polyfills";
// import terserPlugin  from 'rollup-plugin-terser';

const privateKey = fs.readFileSync("./ssl/private.key");
const certificate = fs.readFileSync("./ssl/certificate.pem");

// https://vitejs.dev/config/
export default defineConfig({
    base: "/",
    resolve: {
        alias: {
            "@": path.resolve(__dirname, "./src"),
        },
    },
    build: {
        outDir: 'dist',
        minify: 'terser', // 启用内置的 Terser 压缩
        terserOptions: {
            compress: {
                drop_console: true, // 删除所有的 `console` 语句
                // 其他压缩选项...
            },
            output: {
                comments: false, // 删除所有注释
                // 其他输出选项...
            },
        }
    },

    plugins: [nodePolyfills(), svgr(), react(),
        //     terserPlugin ({
        //     compress: {
        //       drop_console: true, // 删除所有的 `console` 语句
        //       // 其他压缩选项...
        //     },
        //     output: {
        //       comments: false, // 删除所有注释
        //       // 其他输出选项...
        //     },
        // }),
    ],
    server: {
        // https: {
        //   key: privateKey,
        //   cert: certificate,
        // },
        proxy: {
            // "^/api": {
            //     target: "http://192.168.131.197:9022",
            //     changeOrigin: true,
            //     rewrite(path) {
            //         return path.replace(/^\/api/, "");
            //     },
            // },
            "^/api": {
                // target: "http://api-dev.reader.chat:9022",
                // target: "http://1.202.99.120:18877",
                target: "http://10.0.42.60:9099",
                // target: "http://192.168.1.170:9022",
                changeOrigin: true,
                secure: false,
                rewrite(path) {
                    return path.replace(/^\/api/, "");
                },
            },
        },
    },
    css: {
        postcss: {
            plugins: [
                mobile({
                    appSelector: '#root',
                    viewportWidth: 375,
                    maxDisplayWidth: 450,
                    // selectorBlackList: [".ignore-", ".ignore_"],
                })
                // postcsspxtoviewport({
                //     unitToConvert: "px",
                //     viewportWidth: 375,
                //     unitPrecision: 6,
                //     propList: ["*"],
                //     viewportUnit: "vw",
                //     fontViewportUnit: "vw",
                //     selectorBlackList: [".ignore-", ".ignore_"],
                //     minPixelValue: 1,
                //     mediaQuery: true,
                //     replace: true,
                //     exclude: undefined,
                //     include: undefined,
                //     landscape: false,
                //     landscapeUnit: "vw",
                //     landscapeWidth: 568,
                // }),
            ],
        },
    },
});

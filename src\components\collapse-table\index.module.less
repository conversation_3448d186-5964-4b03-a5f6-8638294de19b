.collapseTable {
    --th-text-color: var(--text-main);
    :global {
        table {
            width: 100%;
            table-layout: fixed;
            border-spacing: 14px 10px;
        }
        th {
            color: var(--th-text-color);
            font-size: var(--text-md);
            padding-bottom: 6px;
            text-align: left;
        }
        td {
            color: var(--text-secondary);
            font-size: var(--text-sm);
        }
        .collapseTable__toggleIcon {
            cursor: pointer;
            text-align: center;
            color: var(--primary-color);
            font-size: 15px;
        }
    }
}

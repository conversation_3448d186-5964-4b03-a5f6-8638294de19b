import {
    Html5QrcodeScanner,
    Html5Qrcode,
    Html5QrcodeScannerState,
} from "html5-qrcode";
import { useEffect, useRef, useState } from "react";
import { Toast, SpinLoading } from "antd-mobile";

import {} from "@/utils";

const qrcodeRegionId = "html5qr-code-full-region";

interface Html5QrcodePluginProps {
    shouldScan?: boolean;
    onScanned?: (ret: string) => void;
    onStartError?: (errStr: string) => void;
}

const Html5QrcodePlugin = (props: Html5QrcodePluginProps) => {
    const { shouldScan, onStartError, onScanned } = props;
    const [isLoading, setIsLoading] = useState(true);
    const html5QrCodeRef = useRef<Html5Qrcode | null>(null);

    const scanContainerRef = useRef<HTMLDivElement>(null);
    useEffect(() => {
        const containerDom = scanContainerRef?.current!;

        // TODO 似乎pc端和手机端相反
        const aspectRatio =
            containerDom.clientHeight / containerDom.clientWidth;

        const html5QrCode = new Html5Qrcode(qrcodeRegionId);
        html5QrCodeRef.current = html5QrCode;

        let initFlag = true;
        html5QrCode
            .start(
                { facingMode: "environment" },
                {
                    fps: 10,
                    aspectRatio: aspectRatio,
                    qrbox: (videoWidth, videoHeight) => {
                        let boxWH = Math.min(videoWidth, videoHeight) * 0.8;
                        boxWH = Math.max(boxWH, 10);
                        return { width: boxWH, height: boxWH };
                    },
                },
                (decodedText, decodedResult) => {
                    if (initFlag) {
                        initFlag = false;
                        setIsLoading(false);
                    }
                    onScanned?.(decodedText);
                },
                (errorMessage) => {
                    if (initFlag) {
                        initFlag = false;
                        setIsLoading(false);
                    }
                },
            )
            .catch((err) => {
                console.log(err, "camera start error");

                let errStr = "打开相机失败";

                if (err === "Camera streaming not supported by the browser.") {
                    errStr = "当前环境不支持使用相机";
                }
                if (err.toString().includes("error = NotAllowedError")) {
                    errStr = "无相机权限";
                }
                onStartError?.(errStr);
            });

        return () => {
            const close = () => {
                html5QrCode
                    .stop()
                    .then((ignore) => {
                        console.log("stopped");
                    })
                    .catch((err) => {
                        console.log(err, "stop err");
                    });
            };
            if (
                html5QrCode.getState() === Html5QrcodeScannerState.SCANNING ||
                html5QrCode.getState() === Html5QrcodeScannerState.PAUSED
            ) {
                close();
            } else {
                setTimeout(() => {
                    if (
                        html5QrCode.getState() ===
                            Html5QrcodeScannerState.SCANNING ||
                        html5QrCode.getState() ===
                            Html5QrcodeScannerState.PAUSED
                    ) {
                        close();
                    }
                }, 1000);
            }
        };
    }, []);

    useEffect(() => {
        const html5QrCode = html5QrCodeRef.current;

        if (html5QrCode && shouldScan !== undefined) {
            if (shouldScan) {
                if (html5QrCode.getState() === Html5QrcodeScannerState.PAUSED) {
                    console.log("resume");
                    html5QrCode.resume();
                }
            } else {
                if (
                    html5QrCode.getState() === Html5QrcodeScannerState.SCANNING
                ) {
                    console.log("pause");
                    // html5QrCode.pause();
                }
            }
        }
    }, [shouldScan]);

    return (
        <div
            style={{
                width: "100%",
                height: "100%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
            }}
        >
            <div
                ref={scanContainerRef}
                id={qrcodeRegionId}
                style={{
                    width: "100%",
                }}
            ></div>
            {isLoading && (
                <SpinLoading
                    style={{
                        position: "absolute",
                        top: "50%",
                        left: "50%",
                        transform: "translate(-50%,-50%)",
                        "--size": "48px",
                        "--color": "white",
                    }}
                ></SpinLoading>
            )}
        </div>
    );
};

export default Html5QrcodePlugin;

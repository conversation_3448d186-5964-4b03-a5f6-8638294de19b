.checkListPopup {
    :global {
    }
}

.checkListPopup__trigger {
    color: var(--text-secondary);
    display: flex;
    // justify-content: space-between;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding-top: 3px;
    // &:active{
    //     background-color: var(--active-background-color);
    // }
}

.checkListPopup__header {
    font-size: var(--adm-font-size-9);
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.checkListPopup__checkListContainer {
    max-height: 300px;
    overflow-y: scroll;
}

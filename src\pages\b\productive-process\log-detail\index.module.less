.LogDetail {
    margin: 10px;
    padding: 20px 16px;
    padding-bottom: 58px;
    background: var(--page-block-color);
    border-radius: var(--border-radius);
    --list-item-extra-color: #666;
    :global {
        .adm-list-item-content-extra {
            color: var(--text-main);
        }
    }
    .LogDetail__OutList {
        padding: 0 12px;
    }
    .infoShow {
        margin-top: 16px;
        &__label {
            color: var(--list-item-extra-color);
            margin-bottom: 16px;
        }
        &__value {
            color: var(--text-main);
        }
    }
    .Btn {
      height: 60px;
      margin-top: 25px;
      // border-radius: 10px;
      // padding: 13px 12px 15px 12px;
      font-size: 16px;
      line-height: 19px;
      letter-spacing: 0px;
      background-image: url('@/assets/imgs/bimgs/btn1.png');
      background-size: 104% 106%;
      background-position: center 3px;
      background-repeat: no-repeat;
      // background-color: transparent;
      // background: red;
      border: 0;
      span {
          color: white;
      }
  }
}

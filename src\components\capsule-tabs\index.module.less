.capsuleTabs {
    :global {
        .adm-capsule-tabs-header {
            padding: 0;
            border-bottom: none;
        }

        .adm-capsule-tabs-tab-list {
            // border: 1px solid #eee;
            background-color: #F4F4F4;
            border-radius: 10px;

            .adm-capsule-tabs-tab {
                border-radius: 10px;
            }
            .disabled {
                .adm-capsule-tabs-tab {
                    border-radius: 0 10px 10px 0;
                }
                .adm-capsule-tabs-tab-active {
                    border-radius: 10px;
                }
                
            }
        }

        .adm-capsule-tabs-tab-wrapper {
            padding: 0;
        }

        .adm-capsule-tabs-tab {
            padding: 10px 20px;
            background-color: #F4F4F4;
            color: var(--text-secondary);
        }

        .adm-capsule-tabs-tab-active {
            background-color: var(--adm-color-primary);
            color: var(--adm-color-text-light-solid);
        }
    }
}
import React, { useEffect, useState } from "react";
import {
    create<PERSON><PERSON><PERSON><PERSON><PERSON>er,
    RouterProvider,
    Navigate,
    Outlet,
    useOutlet,
    useOutletContext,
    useLocation,
    useNavigate,
    Link,
} from "react-router-dom";
import { Badge } from "antd-mobile";

import NavBar from "@/components/nav-bar";
import TabBar, { RouteTabBar } from "@/components/tab-bar";
import CacheRouteContainer from "@/components/cache-route-container";

import type { RouteObject } from "react-router-dom";

import iconInbound from "@/assets/imgs/tabs/inbound.png";
import iconInboundSelected from "@/assets/imgs/tabs/inbound-selected.png";
import iconLog from "@/assets/imgs/tabs/log.png";
import iconLogSelected from "@/assets/imgs/tabs/log-selected.png";

import { ReactComponent as LogIcon } from "@/assets/imgs/tabs/svg/log1.svg";
import { ReactComponent as InboundIcon } from "@/assets/imgs/tabs/svg/ru.svg";

import styles from "./index.module.less";

import BScanInbound from "@/pages/b/inboundManagement/scan-inbound";
import BInboundLogList from "@/pages/b/inboundManagement/log-list";
import BInboundLogDetail from "@/pages/b/inboundManagement/inbound-log-detail";
import BReportErrorResult from "@/pages/b/report-error-result";
import NoPermissionPage from "@/pages/403";

import useStore from "@/store";

const PackLayout = () => {
    const [pageTitle, setPageTitle] = useState("");
    const [editPageState, setEditPageState] = useState<{
        disabled: boolean;
        id: string;
    } | null>(null);
    const userMenuPermission = useStore((state) => state.userMenuPermission);
    const navigate = useNavigate();
    const location = useLocation();
    const tabs = [
        {
            key: "scan",
            title: "扫码入库",
            icon: (active: boolean) => (
                // active ? <img src={iconInboundSelected} /> : <img src={iconInbound} />,
                <InboundIcon
                    style={{
                        color: active ? "var(--primary-color)" : "#e7e7e7",
                    }}
                ></InboundIcon>
            ),
        },
        {
            key: "log",
            title: "记录查看",
            icon: (active: boolean) => (
                // active ? <img src={iconLogSelected} /> : <img src={iconLog} />,
                <LogIcon
                    style={{
                        color: active ? "var(--primary-color)" : "#e7e7e7",
                    }}
                ></LogIcon>
            ),
        },
    ];

    if (!userMenuPermission.find((item) => item.perms === "warehouse")) {
        return (
            <div className={`_global_page ${styles.layout}`}>
                <NavBar>{pageTitle}</NavBar>
                <NoPermissionPage></NoPermissionPage>
            </div>
        );
    }
    type PageTitle = "扫码入库" | "入库列表" | "入库详情";

    // 使用类型别名作为 routesMap 的键
    // const routesMap: { [key in PageTitle]: string } = {
    //     扫码入库: "/b/home",
    //     入库列表: "/b/home/<USER>/log",
    //     入库详情: "/b/home/<USER>/log",
    // };
    const routesMap: { [key in PageTitle]: string } = {
        扫码入库: "/b/home",
        入库列表: "/b/home",
        入库详情: "/b/home/<USER>/log",
    };
    // 定义 back 函数并使用 PageTitle 类型
    const back = (pageTitle: PageTitle | string) => {
        // 如果是入库详情页面，需要根据来源决定返回路径
        if (pageTitle === "入库详情") {
            const searchParams = new URLSearchParams(location.search);
            const listType = searchParams.get("listType");

            if (listType === "deprecated") {
                // 从已作废列表进入的详情，返回已作废列表
                navigate("/b/home/<USER>/log?listType=deprecated");
            } else {
                // 从正常列表进入的详情，返回正常列表
                navigate("/b/home/<USER>/log");
            }
            return;
        }

        // 其他页面使用默认逻辑
        const path = routesMap[pageTitle as PageTitle];
        if (path) {
            navigate(path);
        } else {
            console.warn(`Unknown page title: ${pageTitle}`);
        }
    };
    return (
        <div className={`_global_page ${styles.layout}`}>
            <NavBar onBack={() => back(pageTitle)}>{pageTitle}</NavBar>
            <div className="_global_pageScrollContent">
                <Outlet
                    context={{ setPageTitle, setEditPageState, editPageState }}
                ></Outlet>
            </div>
            <RouteTabBar
                tabs={tabs}
                prefix="/b/home/<USER>"
            ></RouteTabBar>
        </div>
    );
};

interface IPackPageWrapperProps {
    title?: string;
    children: React.ReactNode;
}
const PackPageWrapper = ({ children, title = "" }: IPackPageWrapperProps) => {
    const { setPageTitle } = useOutletContext<any>();
    useEffect(() => {
        setPageTitle(title);
    }, [title]);

    return children;
};

export default {
    path: "inboundManagement",
    element: <PackLayout></PackLayout>,
    children: [
        {
            index: true,
            element: <Navigate to="scan" replace></Navigate>,
        },
        {
            path: "scan",
            element: <CacheRouteContainer></CacheRouteContainer>,
            children: [
                {
                    index: true,
                    element: (
                        <PackPageWrapper title="扫码入库">
                            <BScanInbound></BScanInbound>
                        </PackPageWrapper>
                    ),
                },
                {
                    path: "result",
                    element: (
                        <PackPageWrapper title="扫码入库">
                            <BReportErrorResult></BReportErrorResult>
                        </PackPageWrapper>
                    ),
                },
            ],
        },
        {
            path: "log",
            children: [
                {
                    index: true,
                    element: (
                        <PackPageWrapper title="入库列表">
                            <BInboundLogList></BInboundLogList>
                        </PackPageWrapper>
                    ),
                },
                {
                    path: ":id",
                    element: (
                        <PackPageWrapper title="入库详情">
                            <BInboundLogDetail></BInboundLogDetail>
                        </PackPageWrapper>
                    ),
                },
            ],
        },
    ],
} as RouteObject;

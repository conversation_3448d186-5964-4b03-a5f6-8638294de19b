import { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
    Button,
    Input,
    Text<PERSON><PERSON>,
    Picker,
    InfiniteScroll,
    Toast,
    <PERSON>rror<PERSON>lock,
    DotLoading,
    PullToRefresh,
    Ellipsis,
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import { useRequest, useInfiniteScroll, useDebounce } from "ahooks";
import useUrlState from "@ahooksjs/use-url-state";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";

import { INFINITE_LIST_PAGE_SIZE } from "@/config";
import { bSideRequests } from "@/services";

import styles from "./index.module.less";

export default () => {
    const navigate = useNavigate();

    const getListRequest = useInfiniteScroll((d) => {
        const page = d
            ? Math.ceil(d.list.length / INFINITE_LIST_PAGE_SIZE) + 1
            : 1;
        return bSideRequests
            .getInboundList({
                pageIndex: page,
                pageSize: INFINITE_LIST_PAGE_SIZE,
            })
            .then((res) => {
                const listInfo = res?.data?.data || {};
                return {
                    list: listInfo?.records || [],
                    total: listInfo?.total || 0,
                };
            });
    });
    const hasMore =
        getListRequest.data &&
        getListRequest.data.list.length < getListRequest.data.total;

    const data = getListRequest?.data?.list || [];

    const renderList = () => {
        if (getListRequest.loading) {
            return (
                <div
                    style={{
                        paddingTop: "30px",
                        textAlign: "center",
                        color: "var(--adm-color-weak)",
                    }}
                >
                    <div style={{ fontSize: 24, marginBottom: 24 }}>
                        <DotLoading />
                    </div>
                    正在加载数据
                </div>
            );
        }
        if (data.length === 0) {
            return (
                <ErrorBlock
                    image={<div></div>}
                    title="暂无数据"
                    description=""
                />
            );
        }
        return (
            <div>
                <PullToRefresh
                    onRefresh={async () => {
                        await getListRequest.reloadAsync();
                    }}
                >
                    <List className="packLogList">
                        {data.map((item) => {
                            return (
                                <List.Item
                                    key={item?.id}
                                    onClick={() => {
                                        navigate(item?.id?.toString());
                                    }}
                                >
                                    <div className="packLogList__item">
                                        <Ellipsis
                                            style={{
                                                wordBreak: "break-all",
                                            }}
                                            content={
                                                item.warehouseNumber || "-"
                                            }
                                        ></Ellipsis>
                                        <div className="packLogList__item--info">
                                            <div>
                                                {item.createTime
                                                    ? dayjs(
                                                          item.createTime,
                                                      ).format(
                                                          "YYYY-MM-DD HH:mm:ss",
                                                      )
                                                    : "-"}
                                            </div>
                                        </div>
                                    </div>
                                </List.Item>
                            );
                        })}
                    </List>
                </PullToRefresh>
                <InfiniteScroll
                    loadMore={async () => {
                        await getListRequest.loadMoreAsync();
                    }}
                    hasMore={hasMore || false}
                />
            </div>
        );
    };

    return <div className={`${styles.logList}`}>{renderList()}</div>;
};

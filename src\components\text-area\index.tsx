import React, { useState, useCallback, useEffect } from "react";
import {
    Button,
    Input,
    TextArea as AntdTextArea,
    Picker,
    Space,
    Modal,
    ImageUploader,
} from "antd-mobile";
import { AddOutline } from "antd-mobile-icons";
import classNames from "classnames";

import styles from "./index.module.less";

import type { TextAreaProps } from "antd-mobile";

export default function TextArea(props: TextAreaProps) {
    const { className, ...restProps } = props;

    return (
        <AntdTextArea
            className={classNames(styles.TextArea, className)}
            {...restProps}
        ></AntdTextArea>
    );
}

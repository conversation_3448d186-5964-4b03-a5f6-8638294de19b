//out:false
.logList {
    margin: 10px;
    padding: 20px 16px;
    background: var(--page-block-color);
    border-radius: var(--border-radius);

    :global {
        .packLogList {
            margin-top: 25px;

            &__item {
                &--info {
                    font-size: var(--text-sm);
                    color: var(--text-secondary);
                    display: flex;
                    gap: 20px;
                }
            }
        }
    }
}

.Btn {
    height: 60px;
    // margin-top: 25px;
    // border-radius: 10px;
    // padding: 13px 12px 15px 12px;
    font-size: 16px;
    line-height: 19px;
    letter-spacing: 0px;
    background-image: url("@/assets/imgs/bimgs/btn1.png");
    background-size: 104% 106%;
    background-position: center 3px;
    background-repeat: no-repeat;
    // background-color: transparent;
    // background: red;
    border: 0;

    span {
        color: white;
    }
}
.okBtn {
    width: 150px;
    height: 60px;
    // margin-top: 25px;
    // border-radius: 10px;
    // padding: 13px 12px 15px 12px;
    font-size: 16px;
    line-height: 19px;
    letter-spacing: 0px;
    background-image: url("@/assets/imgs/bimgs/btn1.png");
    background-size: 104% 106%;
    background-position: center 3px;
    background-repeat: no-repeat;
    // background-color: transparent;
    // background: red;
    border: 0;

    span {
        color: white;
    }
}

.BtnEdit {
    color: white;
    display: inline-block;
    width: 130px;
    height: 60px;
    cursor: pointer;
    // border-radius: 10px;
    // padding: 13px 12px 15px 12px;
    font-size: 16px;
    line-height: 60px;
    letter-spacing: 0px;
    text-align: center;
    background-image: url("@/assets/imgs/bimgs/btn1.png");
    background-size: 104% 106%;
    background-position: center 3px;
    background-repeat: no-repeat;
    // background-color: transparent;
    // background: red;
}

.result {
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
}

.flexbtn {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
}



.Enable {
    width: 130px;
    height: 60px;
    // margin-top: 25px;
    // border-radius: 10px;
    // padding: 13px 12px 15px 12px;
    font-size: 16px;
    line-height: 19px;
    letter-spacing: 0px;
    background-image: url("@/assets/imgs/bimgs/btn1.png");
    background-size: 104% 106%;
    background-position: center 3px;
    background-repeat: no-repeat;
    // background-color: transparent;
    // background: red;
    border: 0;

    span {
        color: white;
    }
}
.spanright {
    width: 155px;
    display: inline-block;
    text-align: right;
    // border: 1px solid;
}

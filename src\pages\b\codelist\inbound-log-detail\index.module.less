/* 公共基础样式 */
.logDetailBase {
    position: relative; /* 为子元素绝对定位提供参考 */
    margin: 10px;
    padding: 20px 16px;
    padding-bottom: 58px;
    background: var(--page-block-color);
    border-radius: var(--border-radius);
    .adm-list-item-content-extra {
        color: var(--text-main) !important;
        font-size: 14px !important;
    }
    :global {
        .boxCodeList {
            padding-top: 5px;
            border-bottom: solid 1px var(--adm-color-border);

            th {
                color: var(--text-main);
                font-size: var(--text-md);
                font-weight: normal;
            }
        }
    }
}

/* 公共伪元素样式 */
.logDetailBase::after {
    content: "";
    position: absolute;
    top: 0px;
    right: 0px;
    width: 50px; /* 根据实际图片调整 */
    height: 50px; /* 根据实际图片调整 */
    background-size: cover; /* 保持图片比例覆盖元素 */
    background-repeat: no-repeat;
}

/* 错误状态 */
.inboundLogDetail {
    composes: logDetailBase;
}

.inboundLogDetail::after {
    background-image: url("@/assets/qin/icon/suss.png"); /* 错误图标 */
    border-top-right-radius: 10px;
}

/* 成功状态 */
.suss {
    composes: logDetailBase;
}

.suss::after {
    background-image: url("@/assets/qin/icon/err.png"); /* 成功图标 */
    border-top-right-radius: 10px;
}

.otherClassName {
    position: absolute;
    width: 57px;
    height: 57px;
    top: 0;
    right: 0;
    border-radius: 0 8px 0 0;
    background: url("@/assets/qin/icon/suss.png") no-repeat; /* 成功图标 */
    background-size: 100% 100%;
    background-position: 4px -4px;
}
.othererr {
    position: absolute;
    width: 57px;
    height: 57px;
    top: 0;
    right: 0;
    border-radius: 0 8px 0 0;
    background: url("@/assets/qin/icon/err.png") no-repeat; /* 成功图标 */
    background-size: 100% 100%;
    background-position: 4px -4px;
}
.Btn {
    height: 60px;
    margin-top: 25px;
    // border-radius: 10px;
    // padding: 13px 12px 15px 12px;
    font-size: 16px;
    line-height: 19px;
    letter-spacing: 0px;
    background-image: url("@/assets/imgs/bimgs/btn1.png");
    background-size: 104% 106%;
    background-position: center 3px;
    background-repeat: no-repeat;
    // background-color: transparent;
    // background: red;
    border: 0;

    span {
        color: white;
    }
}
.Title {
    font-size: 18px;
    position: relative;
    margin-bottom: 20px;
    span {
        margin-left: 10px;
    }
}

.Title::after {
    content: "";
    display: block;
    width: 100%;
    height: 1px;
    background: var(--adm-color-border); /* 横线颜色 */
    margin-top: 15px; /* 调整横线与文字的距离 */
}
.listItem{
  
}
{"name": "cm-rice-h5", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "start": "npm run dev", "build": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "prettier": "npx prettier --write ."}, "dependencies": {"@ahooksjs/use-url-state": "^3.5.1", "@vant/touch-emulator": "^1.4.0", "antd-mobile": "^5.36.0", "axios": "^1.5.0", "classnames": "^2.3.2", "copy-to-clipboard": "^3.3.3", "create-vite": "^5.2.3", "crypto-js": "^4.1.1", "dayjs": "^1.11.9", "eosjs-ecc": "^4.0.7", "immer": "^10.0.3", "js-md5": "^0.8.3", "jsencrypt": "^3.3.2", "normalize.css": "^8.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.26.2", "rollup-plugin-terser": "^7.0.2", "use-immer": "^0.9.0", "vconsole": "^3.15.1", "zustand": "^4.4.1"}, "devDependencies": {"@types/crypto-js": "^4.1.2", "@types/node": "^20.6.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "html5-qrcode": "^2.3.8", "less": "^4.2.0", "postcss-mobile-forever": "^4.0.0", "postcss-px-to-viewport": "^1.1.1", "prettier": "^3.0.3", "typescript": "^5.0.2", "vite": "^6.2.5", "vite-plugin-node-polyfills": "^0.15.0", "vite-plugin-svgr": "^3.2.0"}}
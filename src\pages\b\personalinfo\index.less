//out:false
.person_info_box {
    height: 100%;
    // background: #F1F4EE;
    background:
        url("@/assets/imgs/bimgs/info-background1.png") no-repeat center/100%
            100%,
        linear-gradient(180deg, #f1f1f1 0%, #ededed 100%);

    .back_img {
        height: 200px;
        background:
            url("@/assets/imgs/bimgs/personal-img.png") no-repeat center/100%
                100%,
            linear-gradient(180deg, #f1f1f1 0%, #ededed 100%);

        .adm-nav-bar {
            padding-top: 20px;
        }
    }

    .info_div {
        width: 90%;
        height: 320px;
        border-radius: 10px;
        background: #ffff;
        margin: 0 auto;
        position: relative;
        bottom: 90px;

        .avatar_div {
            display: flex;
            flex-direction: column;
            align-items: center;

            .avatar {
                width: 100px;
                height: 100px;

                img {
                    width: 100%;
                    height: 100%;
                }
            }

            .user_name {
                padding: 0 7px 0 7px;
            }
        }

        .info_text {
            padding: 0px 35px;

            .info_name {
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }

            p {
                border-bottom: 1.5px solid #e5e5e6;
                height: 25px;
            }

            .flex_p {
                display: flex;
                justify-content: space-between;

                span:nth-child(2) {
                    margin-right: 20px;
                    cursor: pointer;
                }
            }

            .time_text {
                color: #989898;
                font-size: 11px;
            }
        }
    }
}

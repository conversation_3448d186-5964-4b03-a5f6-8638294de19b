import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
    Button,
    Input,
    TextArea,
    Picker,
    Space,
    DotLoading,
    Ellipsis,
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
    UpOutline,
    DownOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import { useRequest, useSetState } from "ahooks";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";
import CollapseTable from "@/components/collapse-table";

import iconScanQrCode from "@/assets/imgs/scan-qrcode.png";

import { bSideRequests } from "@/services";
import { REPOSITORY_TYPE_CONSTANTS } from "@/config";

import styles from "./index.module.less";

export default () => {
    const navigate = useNavigate();
    const { id } = useParams();

    const getInboundDetailRequest = useRequest(
        async () => {
            if (!id) {
                throw new Error("缺少id");
            }
            const res = await bSideRequests.getCodeListDetail(id);
            console.log(res?.data.data, "res");

            return res;
        },
        {
            refreshDeps: [id],
        },
    );
    const inboundDetail = getInboundDetailRequest.data?.data?.data || {};

    if (getInboundDetailRequest.loading) {
        return (
            <div
                style={{
                    paddingTop: "50px",
                    textAlign: "center",
                    color: "var(--adm-color-weak)",
                }}
            >
                <div style={{ fontSize: 24, marginBottom: 24 }}>
                    <DotLoading />
                </div>
                正在加载数据
            </div>
        );
    }
    console.log(inboundDetail.maxQuantity, "inboundDetail.maxQuantity");

    return (
        <div className="listItem">
            <div className={`${styles.logDetailBase}`}>
                {inboundDetail.maxQuantity == 0 ? (
                    <div className={`${styles.otherClassName}`}></div>
                ) : inboundDetail.maxQuantity >= inboundDetail.searchCount ? (
                    <div className={`${styles.otherClassName}`}></div>
                ) : (
                    <div className={`${styles.othererr}`}></div>
                )}

                <div className={`${styles.Title}`}>
                    <span>溯源码详情</span>
                </div>
                <List>
                    <List.Item
                        extra={
                            <Ellipsis
                                content={inboundDetail?.code || "-"}
                            ></Ellipsis>
                        }
                    >
                        溯源码
                    </List.Item>
                    <List.Item extra={inboundDetail?.packNumber || "-"}>
                        所属码包
                    </List.Item>

                    <List.Item
                        extra={
                            inboundDetail.generateTime
                                ? dayjs(inboundDetail.generateTime).format(
                                      "YYYY-MM-DD HH:mm:ss",
                                  )
                                : "-"
                        }
                    >
                        生码时间
                    </List.Item>
                    <List.Item
                        extra={inboundDetail?.product?.productName || "-"}
                    >
                        所属产品
                    </List.Item>
                    <List.Item extra={inboundDetail?.productionBatch || "-"}>
                        生产批次
                    </List.Item>
                    <List.Item extra={inboundDetail?.maxQuantity || "-"}>
                        单码扫码限制
                    </List.Item>
                    <List.Item extra={inboundDetail?.searchCount}>
                        查询次数
                    </List.Item>
                    {/* <List.Item
                    extra={
                        ![undefined, null].includes(
                            inboundDetail?.storehouseType,
                        )
                            ? // @ts-ignore
                              REPOSITORY_TYPE_CONSTANTS
                                  .REPOSITORY_TYPE_MAP_BY_VALUE[
                                  inboundDetail.storehouseType
                              ].name
                            : "-"
                    }
                >
                    仓库类型
                </List.Item> */}
                    <List.Item>
                        {inboundDetail?.traceCodeRule == 2 &&
                        inboundDetail?.state == 0 &&
                        inboundDetail?.productId ? (
                            <Button
                                // loading={inboundScanEntryRequest.loading}
                                onClick={() => {
                                    // if (scannedBoxCodeInfo.length === 0) {
                                    //     Toast.show("请扫描箱码");
                                    //     return;
                                    // }
                                    // showConfirmModal({
                                    //     title: "确认上传",
                                    //     onConfirm() {
                                    //         handleEntryRequest();
                                    //     },
                                    // });
                                    navigate(
                                        "/b/home/<USER>/scan?step=scan&id=" +
                                            id,
                                    );
                                }}
                                style={{ marginTop: 100 }}
                                className={`${styles.Btn}`}
                                fill="none"
                                color="primary"
                                shape="rounded"
                                block
                                type="submit"
                            >
                                编辑
                            </Button>
                        ) : null}
                    </List.Item>
                </List>

                {/* <div className="boxCodeList">
                <CollapseTable
                    columns={[
                        {
                            title:
                                "箱码" +
                                (inboundDetail?.boxTo?.length &&
                                inboundDetail.boxTo.length > 0
                                    ? `（共${inboundDetail.boxTo.length}个）`
                                    : ""),
                            dataIndex: "boxCode",
                            width: "50%",
                        },
                        {
                            title: "产品名称",
                            dataIndex: "productName",
                            width: "40%",
                        },
                        {
                            width: "10%",
                        },
                    ]}
                    dataSource={inboundDetail?.boxTo || []}
                ></CollapseTable>
            </div> */}
            </div>
        </div>
    );
};

import { useState, useEffect, useRef, useCallback } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
    Button,
    Input,
    TextA<PERSON>,
    Picker,
    Space,
    Mask,
    Toast,
    Ellipsis,
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
    UpOutline,
    DownOutline,
    LeftOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import { useRequest, useSetState } from "ahooks";
import { useImmer } from "use-immer";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";
import CollapseTable from "@/components/collapse-table";
import QRCodeScan from "@/components/qrcode-scan";
import EllipsisTraceCode from "@/components/ellipsis-trace-code";

import { useScanQRCode } from "@/hooks";
import {
    bSideRequests,
    getPackCodeInfoByPackCode,
    getTraceCodeInfoByTraceCodeId,
    BoxCodeVerifyModule,
} from "@/services";
import { showToastLoading, showConfirmModal } from "@/utils";
import { MAX_SCAN_LENGTH } from "@/config";

import iconScanQrCode from "@/assets/imgs/scan-qrcode.png";
import { ReactComponent as SvgScanQrCode } from "@/assets/imgs/scan-qrcode.svg";

import styles from "./index.module.less";

import type { ToastLoadingHandler } from "@/utils";

interface IScanPackState {
    boxCode: string | null;
    traceCode: {
        id: number | string;
        code: string;
        name: string;
    }[];
}

const ScanPack = () => {
    const navigate = useNavigate();

    const { qrCodeScanElement, startScanQrCode, closeScanQrCode } =
        useScanQRCode();

    const [scanPackState, setScanPackState] = useImmer<IScanPackState>({
        boxCode: null,
        traceCode: [],
    });
    const scanPackStateRef = useRef(scanPackState);
    scanPackStateRef.current = scanPackState;
    const getScanPackState = useCallback(() => scanPackStateRef.current, []);

    const toastLoadingHandlerRef = useRef<ToastLoadingHandler | null>(null);
    const packScanEntryRequest = useRequest(bSideRequests.packScanEntry, {
        manual: true,
        onError(ret: any) {
            toastLoadingHandlerRef?.current?.close?.();
            const errMsg = ret?.response?.data?.message || "上报失败";
            navigate(`result?msg=${errMsg}`);
        },
        onSuccess() {
            toastLoadingHandlerRef?.current?.close?.();
            setScanPackState((state) => {
                state.boxCode = null;
                state.traceCode = [];
            });
            Toast.show({
                icon: "success",
                content: "上报成功",
            });
        },
    });

    const getPackCodeInfoByPackCodeRequest = useRequest(
        getPackCodeInfoByPackCode,
        {
            manual: true,
        },
    );
    const getTraceCodeInfoByTraceCodeIdRequest = useRequest(
        getTraceCodeInfoByTraceCodeId,
        {
            manual: true,
        },
    );

    const handleTraceCodeContent = async (
        traceCodeUrl: string,
        continueScan: () => void,
    ) => {
        try {
            const productId = new URL(traceCodeUrl).searchParams.get(
                "traceCodeId",
            );
            if (!productId) {
                throw new Error("无法从二维码中找到ID");
            }
            const { close: closeLoading } = Toast.show({
                icon: "loading",
            });
            const continueScanWithMessage = (msg: string, icon?: string) => {
                closeLoading();
                Toast.show({
                    icon: icon,
                    content: msg,
                    duration: 1000,
                    afterClose() {
                        continueScan();
                    },
                });
            };
            const traceCodeInfoRet =
                await getTraceCodeInfoByTraceCodeIdRequest.runAsync(productId);
            const sourceInfo = traceCodeInfoRet?.data?.data || {};
            if (sourceInfo?.id) {
                const existIds = getScanPackState().traceCode.map(
                    (item) => item.id,
                );
                if (existIds.includes(sourceInfo.id)) {
                    continueScanWithMessage("该溯源码已添加");
                } else {
                    setScanPackState((state) => {
                        state.traceCode.push({
                            id: sourceInfo.id,
                            code: sourceInfo.code,
                            name: sourceInfo.productName,
                        });
                    });
                    continueScanWithMessage(`${sourceInfo.code} 扫描成功`);
                    if (
                        getScanPackState().traceCode.length + 1 >=
                        MAX_SCAN_LENGTH
                    ) {
                        closeScanQrCode();
                    }
                }
            } else {
                continueScanWithMessage("获取溯源码信息失败");
            }
        } catch (err: any) {
            const errMsg = err?.response?.data?.message;

            Toast.show({
                icon: "fail",
                content: errMsg || "解析溯源码失败",
                duration: 1000,
                afterClose() {
                    continueScan();
                },
            });
        }
    };

    return (
        <div className={`${styles.ScanPack}`}>
            <div className="scanBoxRetContainer outTable-extraPadding ">
                <div className="scanRetHeader">箱码</div>
                {scanPackState.boxCode ? (
                    <div className="scanRet">
                        <Ellipsis
                            className="scanRet__code"
                            direction="end"
                            content={scanPackState.boxCode}
                        ></Ellipsis>
                        <Button
                            onClick={() => {
                                setScanPackState((state) => {
                                    state.boxCode = null;
                                });
                            }}
                            fill="none"
                            color="danger"
                            className="deleteBtn"
                        >
                            删除
                        </Button>
                    </div>
                ) : (
                    <Button
                        onClick={async () => {
                            startScanQrCode(
                                async (boxCodeStr, continueScan) => {
                                    try {
                                        const boxInfoRet =
                                            await getPackCodeInfoByPackCodeRequest.runAsync(
                                                {
                                                    boxCode: boxCodeStr,
                                                    optState:
                                                        BoxCodeVerifyModule.PACK,
                                                },
                                                false,
                                            );
                                        const boxInfo =
                                            boxInfoRet?.data?.data || {};
                                        setScanPackState((state) => {
                                            state.boxCode = boxInfo?.boxCode;
                                        });
                                        closeScanQrCode();
                                    } catch (err) {
                                        continueScan();
                                    }
                                },
                            );
                        }}
                        className="scanBtn"
                        color="primary"
                        fill="outline"
                    >
                        <SvgScanQrCode className="scanBtnIcon"></SvgScanQrCode>
                        <div>扫描箱码</div>
                    </Button>
                )}
            </div>
            <div className="scanBoxRetContainer" style={{ marginTop: 22 }}>
                <div className="scanRetHeader outTable-extraPadding">
                    溯源码
                    {scanPackState.traceCode.length > 0 &&
                        `（共${scanPackState.traceCode.length}个）`}
                </div>
                {scanPackState.traceCode.length > 0 && (
                    <CollapseTable
                        columns={[
                            {
                                title: "产品名",
                                dataIndex: "name",
                                width: "40%",
                            },
                            {
                                title: "溯源码",
                                dataIndex: "code",
                                width: "40%",
                                render(code: string) {
                                    return (
                                        <EllipsisTraceCode codeText={code} />
                                    );
                                },
                            },
                            {
                                width: "20%",
                                style: {
                                    display: "flex",
                                    justifyContent: "flex-end",
                                },
                                render(_: any, record: any, index: any) {
                                    return (
                                        <Button
                                            onClick={() => {
                                                setScanPackState((state) => {
                                                    state.traceCode.splice(
                                                        index,
                                                        1,
                                                    );
                                                });
                                            }}
                                            fill="none"
                                            color="danger"
                                            className="deleteBtn"
                                        >
                                            删除
                                        </Button>
                                    );
                                },
                            },
                        ]}
                        dataSource={scanPackState.traceCode}
                    ></CollapseTable>
                )}
                {scanPackState.traceCode.length < MAX_SCAN_LENGTH && (
                    <Button
                        loading={getTraceCodeInfoByTraceCodeIdRequest.loading}
                        className="scanBtn"
                        color="primary"
                        fill="outline"
                        onClick={async () => {
                            startScanQrCode((traceCodeUrl, continueScan) => {
                                handleTraceCodeContent(
                                    traceCodeUrl,
                                    continueScan,
                                );
                            });
                        }}
                    >
                        <SvgScanQrCode className="scanBtnIcon"></SvgScanQrCode>
                        <div>扫描溯源码</div>
                    </Button>
                )}
            </div>
            <Button
                loading={packScanEntryRequest.loading}
                onClick={() => {
                    if (!scanPackState.boxCode) {
                        Toast.show("请扫描箱码");
                        return;
                    }
                    if (scanPackState.traceCode.length === 0) {
                        Toast.show("请扫描溯源码");
                        return;
                    }
                    showConfirmModal({
                        title: "确认上传",
                        onConfirm() {
                            toastLoadingHandlerRef.current = showToastLoading({
                                duration: 0,
                                content: "正在上传",
                            });
                            packScanEntryRequest.run({
                                boxCode: scanPackState.boxCode!,
                                traceCode: scanPackState.traceCode.map(
                                    (item) => item.id,
                                ),
                            });
                        },
                    });
                }}
                style={{ marginTop: 100 }}
                color="primary"
                shape="rounded"
                block
            >
                上报
            </Button>
            {qrCodeScanElement}
        </div>
    );
};

export default ScanPack;

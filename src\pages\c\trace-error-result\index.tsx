import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button, Input, TextArea, <PERSON><PERSON>, <PERSON>, Mask } from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
    UpOutline,
    DownOutline,
    LeftOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import useUrlState from "@ahooksjs/use-url-state";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";
import CollapseTable from "@/components/collapse-table";
import QRCodeScan from "@/components/qrcode-scan";

import { useScanQRCode } from "@/hooks";

import iconScanQrCode from "@/assets/imgs/scan-qrcode.png";
import { ReactComponent as SvgScanQrCode } from "@/assets/imgs/scan-qrcode.svg";

import styles from "./index.module.less";

import iconTraceError from "@/assets/imgs/c/trace-error-icon.png";

const TraceErrorResult = () => {
    const navigate = useNavigate();
    const [{ msg }] = useUrlState<any>();

    return (
        <div className={`${styles.ScanPack}`}>
            <div className={styles.iconErrorContainer}>
                <img src={iconTraceError} alt="" />
            </div>
            <div className={styles.errInfo}>{msg}</div>
            <div className={styles.call}>如有疑问请联系生产厂商</div>
        </div>
    );
};

export default TraceErrorResult;

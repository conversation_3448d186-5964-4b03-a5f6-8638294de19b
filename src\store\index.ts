import { create } from "zustand";
import { devtools } from "zustand/middleware";

interface State {
    isLogin: boolean;
    login: () => void;
    logout: () => void;
    userInfo: null | Record<string, any>;
    userMenuPermission: any[];
    decryptedPrivateKey: null | string;
    updateUserInfo: (userInfo: State["userInfo"]) => void;
    updateUserMenuPermission: (
        menuPermission: State["userMenuPermission"],
    ) => void;
}

const useStore = create<State>()(
    devtools((set) => ({
        isLogin: false,
        userInfo: null,
        userMenuPermission: [],
        decryptedPrivateKey: null,
        login: () => set((state) => ({ isLogin: true })),
        logout: () =>
            set((state) => ({
                isLogin: false,
                userInfo: null,
                decryptedPrivateKey: null,
                userMenuPermission: [],
            })),
        updateUserInfo: (userInfo: State["userInfo"]) =>
            set((state) => ({ userInfo: userInfo })),
        updateUserMenuPermission: (
            menuPermission: State["userMenuPermission"],
        ) => set((state) => ({ userMenuPermission: menuPermission })),
    })),
);

export interface ThemeState {
    theme: "blue" | "green";
    changeTheme: (theme: ThemeState["theme"]) => void;
}
export const useTheme = create<ThemeState>()(
    devtools((set) => ({
        theme: "blue",
        changeTheme: (theme: ThemeState["theme"]) =>
            set(() => ({ theme: theme })),
    })),
);

export default useStore;

//out:false
.cTrace {
    overflow: hidden;
    min-height: 100vh;
    // background:
    //     url("@/assets/imgs/c/syxx.png") no-repeat center top/100% url("@/assets/imgs/c/syxx.png") no-repeat center top/100%,
    //     #f2f2f2;
    background-image:
        url("@/assets/imgs/c/sy1.png"),
        url("@/assets/imgs/bimgs/info-background.png");
    background-size:
        100%,
        100% 100%;
    /* 定义每张图片的大小 */
    background-position:
        top left,
        bottom right;
    /* 定义每张图片的位置 */
    background-repeat: no-repeat, no-repeat;
    /* 禁止图片重复 */
    image-rendering: -webkit-optimize-contrast; /* For Chrome */
    image-rendering: crisp-edges; /* For Firefox */
    line-height: 1;
    .traceSearchInfo__value {
        margin-top: 8px;
        width: 100%;
        font-weight: 400 !important;
        font-size: 14px !important;
        color: #4d4d4d !important;
        white-space: nowrap !important;
        // text-align: center;
    }
    .traceFunction {
        margin-top: 135px;
        display: flex;
        justify-content: center;

        &__info {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 7px 15px 7px 15px;
            border-radius: 14px;
            background: #ffffff;
            font-size: 12px;
            color: var(--primary-color);
        }

        &__icon {
            width: 15px;
        }
    }

    .main {
        padding: 45px 10px 20px 10px;

        .traceSearchInfo {
            // width: 70%;

            margin: 0 auto;

            padding: 10px 24px 10px 24px;

            // background-color: rgba(255, 255, 255);

            // text-align: center;

            background-color: #fdeec9;

            @supports (backdrop-filter: blur(16px)) {
                backdrop-filter: blur(16px);
                background-color: #e5edd5;
            }

            border-radius: 8px;

            display: flex;

            flex-direction: column;

            gap: 18px;

            line-height: 1;

            &__item {
                display: flex;

                align-items: center;

                gap: 8px;

                font-size: 12px;

                color: #88a644; //#88a644
            }

            &__label {
                width: 50px;

                text-align-last: justify;
            }

            &__value {
                font-size: 14px;

                font-weight: 500;
            }

            &__link {
                display: flex;

                align-items: center;

                text-decoration: none;

                color: #222222;
            }
        }
        .traceFunctionBg {
            background: rgba(255, 255, 255, 0.08);
            box-shadow: inset 0px 4px 34px 0px #ffffff;
            padding: 3px;
            margin: 40px auto 0;
        }
        .traceFunction__suss {
            margin: 60px auto;
            padding: 10px 0 10px 20px;

            height: 120px;

            margin-bottom: 10px;

            border-radius: 4px;
            opacity: 1;

            // background: rgba(128, 169, 50, 0.8); //rgba(128, 169, 50, 0.8)
            background: rgba(255, 255, 255, 0.08);
            box-shadow: inset 0px 4px 34px 0px #ffffff;

            // border: 1px solid #88a644; //#88a644

            display: flex; /* 使用flex布局 */

            align-items: center;

            box-sizing: border-box;

            .traceFunction__img {
                width: 75px; /* 根据实际情况设置宽度 */

                height: 75px; /* 保持图片比例 */

                margin-right: 10px; /* 图片与文字之间留一些间距 */

                background: url("@/assets/imgs/c/suss.png") no-repeat;

                background-size: 100% 100%;
            }

            // img {

            //   max-width: 100px; /* 根据实际情况设置宽度 */

            //   height: auto; /* 保持图片比例 */

            //   margin-right: 10px; /* 图片与文字之间留一些间距 */

            // }

            div {
                font-family: PingFang SC;

                font-size: 18px;

                font-weight: 500;

                line-height: 24px;

                text-transform: capitalize;

                letter-spacing: 0.015em;

                color: #000;
            }
        }
        .traceFunction__suss1 {
            padding: 10px 0 10px 20px;

            height: 40px;

            margin-bottom: 10px;

            border-radius: 4px;
            opacity: 1;

            // background: rgba(128, 169, 50, 0.8); //rgba(128, 169, 50, 0.8)
            // background: rgba(255, 255, 255, 0.08);
            // box-shadow: inset 0px 4px 34px 0px #ffffff;

            // border: 1px solid #88a644; //#88a644

            display: flex; /* 使用flex布局 */

            align-items: center;

            box-sizing: border-box;

            .traceFunction__img1 {
                width: 42px; /* 根据实际情况设置宽度 */
                margin-left: 48px;
                height: 42px; /* 保持图片比例 */

                margin-right: 10px; /* 图片与文字之间留一些间距 */

                background: url("@/assets/imgs/c/suss.png") no-repeat;

                background-size: 100% 100%;
            }
            .traceSearchInfo__value {
                margin-top: 8px;
                width: 100%;
                font-weight: 400 !important;
                font-size: 14px !important;
                color: #4d4d4d !important;
                text-align: center !important;
                white-space: nowrap !important;
            }
            // img {

            //   max-width: 100px; /* 根据实际情况设置宽度 */

            //   height: auto; /* 保持图片比例 */

            //   margin-right: 10px; /* 图片与文字之间留一些间距 */

            // }

            div {
                font-family: PingFang SC;

                font-size: 18px;

                font-weight: 500;

                line-height: 24px;

                text-transform: capitalize;

                letter-spacing: 0.015em;

                color: #000;
            }
        }
        .traceFunction__info {
            margin: 60px auto;
            padding: 10px 0 10px 20px;
            height: 120px;

            margin-bottom: 10px;

            border-radius: 4px;

            opacity: 1;

            // background: rgba(255, 170, 0, 0.8); //rgba(255, 170, 0, 0.8)
            background: rgba(255, 255, 255, 0.08);
            box-shadow: inset 0px 4px 34px 0px #ffffff;

            // border: 1px solid #ffaa00;

            display: flex; /* 使用flex布局 */

            align-items: center;

            box-sizing: border-box;

            .traceFunction__info_img {
                width: 75px; /* 根据实际情况设置宽度 */

                height: 75px; /* 保持图片比例 */

                margin-right: 10px; /* 图片与文字之间留一些间距 */

                background: url("@/assets/imgs/c/info.png") no-repeat;

                background-size: 100% 100%;
            }

            // img {

            //   max-width: 100px; /* 根据实际情况设置宽度 */

            //   height: auto; /* 保持图片比例 */

            //   margin-right: 10px; /* 图片与文字之间留一些间距 */

            // }

            div {
                font-family: PingFang SC;

                font-size: 18px;

                font-weight: 500;

                color: #000;

                .traceFunction__info_text {
                    font-size: 8px;

                    margin-top: 5px;
                }
            }
        }
        .traceFunction__info1 {
            // margin: 110px auto;
            padding: 10px 0 10px 20px;
            height: 40px;

            margin-bottom: 10px;

            border-radius: 4px;

            opacity: 1;

            // background: rgba(255, 170, 0, 0.8); //rgba(255, 170, 0, 0.8)
            // background: rgba(255, 255, 255, 0.08);
            // box-shadow: inset 0px 4px 34px 0px #ffffff;

            // border: 1px solid #ffaa00;

            display: flex; /* 使用flex布局 */

            align-items: center;

            box-sizing: border-box;

            .traceFunction__info_img1 {
                width: 42px; /* 根据实际情况设置宽度 */

                height: 42px; /* 保持图片比例 */
                margin-left: 48px;
                margin-right: 10px; /* 图片与文字之间留一些间距 */

                background: url("@/assets/imgs/c/info.png") no-repeat;

                background-size: 100% 100%;
            }
            .traceSearchInfo__value {
                margin-top: 8px;
                width: 100%;
                font-weight: 400 !important;
                font-size: 14px !important;
                color: #4d4d4d !important;
                text-align: center !important;
                white-space: nowrap !important;
            }
            // img {

            //   max-width: 100px; /* 根据实际情况设置宽度 */

            //   height: auto; /* 保持图片比例 */

            //   margin-right: 10px; /* 图片与文字之间留一些间距 */

            // }

            div {
                font-family: PingFang SC;

                font-size: 18px;

                font-weight: 500;

                color: #000;

                .traceFunction__info_text {
                    font-size: 8px;

                    margin-top: 5px;
                }
            }
        }
    }

    .traceTabs {
        margin-top: 12px;
        --title-font-size: var(--text-md);
        --content-padding: 0;

        :global {
            .adm-tabs-header {
                background: #ffffff;
                border-radius: 8px;
                padding: 0 12px;

                .adm-tabs-tab-active {
                    color: var(--tabs-primary-color);
                    background: url("@/assets/imgs/c/yuan2.png");
                    // var(--page-bg-color);
                    // background-size: 30%;
                    /* 定义每张图片的大小 */
                    background-position: center right;
                    /* 定义每张图片的位置 */
                    background-repeat: no-repeat;
                    /* 禁止图片重复 */
                    line-height: 1;
                }

                .adm-tabs-tab-line {
                    background-color: #fff;
                }
            }

            //   ._traceBlock__container_nne8r_1 {
            //     margin-top: 0;
            //     border-radius: none;
            //     background: none;
            // }
            .info_detail {
                margin: min(2.667vw, 12px);
                border-radius: 10px;
                background: #ffffff;
                // background-color: rgba(255, 255, 255, 0.5);
            }

            .adm-tabs-tab {
                padding: 13px 0;
            }

            ._traceBlock__container_nne8r_1 {
                opacity: 0.8;
                margin-top: 0;
                border-radius: none;
                background: none;
            }
        }
    }

    .introText {
        white-space: pre-wrap;
        font-size: 14px;
        line-height: 20px;
        color: #909090;
    }
}

.imgbox {
    width: 100%;
    height: 100%;
}

// .my_video_wrap{
//   position: absolute !important;
//   z-index: 88;
// }
.video_wrap {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 88;

    & > span {
        position: absolute;
        width: 34px;
        height: 31px;
        left: 0;
        top: 0;
        color: #fff;
        z-index: 88;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }
}

.adm-selector-item-active,
.adm-selector-item-multiple-active {
    background: #fff;
    color: #000 !important;
}
.suggest {
    padding-left: 17px;
}
.ratingPopup {
    .adm-list-item-content {
        border-top: none !important;
    }
}
.feedback {
    width: 60px;
    height: 60px;
    box-sizing: border-box;
    // padding: 10px;
    border-radius: 50%;
    background: #e1d7c7;
    font-size: 10px;
    color: #feb300;
    text-align: center;
    padding-top: 10px;
    img {
        width: 20px;
        height: 20px;
        margin-bottom: 3px;
    }
}
.dic {
    width: 92%;
    height: 353px;
    margin: 0 auto;
    border-radius: 0;
    padding: 20px 5px;
    // padding: "40px 20px";
    text-align: center;
    background: url("@/assets/imgs/c/dic.png") no-repeat;
    background-size: 100% 100%;
    .dicTitle {
        margin: 10px auto;
        width: 119px;
        height: 31px;
        background: url("@/assets/imgs/c/title.png") no-repeat;
        color: #fdb72c;
        font-size: 16px;
        line-height: 35px;
    }
}
.dicName {
    margin: 10px auto;
    width: 300px;
    // height: 40px;
    border-radius: 4px;
    background: #fdeec9;
    text-align: center;
    font-size: 14px;
    padding: 10px;
    box-sizing: border-box;
}
.traceFunction {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    padding: 0 10px;
    width: 260px;
    margin: 60px auto;
    gap: 20px 30px;
    img {
        width: 15px;
        height: 15px;
        margin-right: 8px;
    }
    > div {
        box-sizing: border-box;
    }
}

.traceBlock__container {
    margin-top: 12px;
    border-radius: 8px;
    background: #ffffff;
}
.traceBlock__itemContainer {
    &--more {
        padding-left: 23px;
    }

    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 18px;

    .traceBlock__groupTitle {
        font-family: Source <PERSON> San<PERSON>;
        margin-left: -8px;
        color: #4D4D4D;
        font-weight: 350;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 6px;
    }
    .ignore_traceBlock__item {
        font-family: Source Han Sans;
        display: flex;
        justify-content: space-between;
        gap: 10px;
        font-size: 14px;
        line-height: 17px;
        .traceBlock__label {
            width: 46%;
            font-size: 14px;
            font-weight: 500;
            color: #757575;
        }
        .traceBlock__value {
            flex: 1;
            display: flex;
            justify-content: flex-end;
            word-break: break-all;
            color: #4D4D4D;
            // text-align: right;
            text-align: justify;
        }
    }
}

.showMoreBtn {
    --border-width: 0;
    --text-color: var(--primary-color);
    --background-color: #dee5ff;
    padding: 9px 12px;
    font-size: 12px;
    margin-top: 20px;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
}

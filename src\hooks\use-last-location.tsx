import React, { useContext, useEffect, useRef } from "react";
import { Location, useLocation, Outlet } from "react-router-dom";

const LastLocationContext = React.createContext<Location | undefined>(
  undefined,
);

interface LastLocationProviderProps {
  lastLocation?: Location;
}

export const LastLocationProvider: React.FC<
  LastLocationProviderProps
> = ({ }) => {
  const location = useLocation();
  const lastLocation = useRef<Location>();
  useEffect(() => {
    let firstlogin = sessionStorage.getItem('firstlogin')
    if ((firstlogin&&location.pathname!="/b/editpassword")||(location.pathname=="/b/login")) {
      sessionStorage.clear();
      localStorage.clear();
    }
    lastLocation.current = location;

  }, [location]);

  return (
    <LastLocationContext.Provider value={lastLocation.current}>
      <Outlet></Outlet>
    </LastLocationContext.Provider>
  );
};

export default function useLastLocation() {
  return useContext(LastLocationContext);
}

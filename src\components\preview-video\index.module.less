.PreviewVideo {
    cursor: pointer;
    position: relative;
    video {
        vertical-align:middle;
        max-width: 100%;
        height: auto;
    }
    .playBtnModal {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        justify-content: center;
        align-items: center;
        .playBtnContainer {
            padding: 10px;
            background: white;
            border-radius: 50%;
        }
    }
}

.videoPreviewModal {
    --adm-center-popup-max-width: 100vw;
    :global {
        .adm-center-popup-wrap {
            width: 100%;
        }
        .adm-modal-body {
            padding-top: 0;
            background: none;
            border-radius: 0px;
        }
        .adm-modal-content {
            padding: 0;

            .videoContainer {
                width: 100%;
                display: flex;
                justify-content: center;
            }
            video {
                max-width: 100%;
                max-height: 70vh;
            }
        }
        .adm-modal-footer-empty {
            height: 0;
        }
    }
}

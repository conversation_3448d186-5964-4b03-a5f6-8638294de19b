import React, { useEffect, useState } from "react";
import { Image, ImageViewer } from "antd-mobile";

import styles from "./index.module.less";

interface IPreviewImageProps {
    images: string[];
    style?: React.CSSProperties;
    className?: string;
    preview?: boolean;
}
function PreviewImage(props: IPreviewImageProps) {
    const { images, preview = true, ...restProps } = props;
    console.log('images',images)
    console.log('images===',images[0].length)
    // const [picture, setPicture] = useState<any>();
    // useEffect(async ()=>{
    //     const pic = await decryptedUrl(images[0])
    //     setPicture(pic)
    //     console.log('pic====',pic)
    // },[])
    

    return (
        <div className={styles.PreviewImage}>
           
            {images.length > 1 && (
                <div className={styles.imageNumber}>{images.length}张</div>
            )}
             {images.length >= 1 && Array.isArray(images[0])&&images[0].length>1 && (
                <div>
                    <div className={styles.imageNumber}>{images[0].length}张</div>
                        <Image
                        src={images[0][0]}
                        onClick={() => {
                            if (preview) {
                                ImageViewer.Multi.show({
                                    images: [...images[0]],
                                });
                            }
                        }}
                        {...restProps}
                    ></Image>
                </div>
            )}
            {!(images.length >= 1 && Array.isArray(images[0])&&images[0].length>1)&&<Image
                src={images[0]}
                onClick={() => {
                    if (preview) {
                        ImageViewer.Multi.show({
                            images: images,
                        });
                    }
                }}
                {...restProps}
            ></Image>}
        </div>
    );
}

export default PreviewImage;

// import React from 'react'
import './index.less'
import { useNavigate } from "react-router-dom";
import { Image, } from "antd-mobile";
import Tx from "@/assets/imgs/bimgs/tx.png";
import useStore from "@/store";

import dayjs from "dayjs";
import { NavBar, Toast } from 'antd-mobile'


export default () => {
  // const userInfo = useStore<any>((state) => state.userInfo);
  const userInfo = JSON.parse(sessionStorage.userInfo)


  const navigate = useNavigate();
  const back = () => {
    navigate("/b/home");
  }
  const changephone = () => {
    navigate("/b/changephone");
  }
  const changepassword = () => {
    navigate("/b/changepassword");
  }
  return (
    <div className='person_info_box'>
      <div className='back_img'>
        <NavBar className='nav' onBack={back}>个人信息</NavBar>
      </div>
      <div className='info_div'>
        <div className='avatar_div'>
          <div className='avatar'>
            <Image
              src={Tx}
            ></Image>
          </div>
          <p className='user_name'>{userInfo && userInfo.userName}</p>
        </div>

        <div className='info_text'>
          <p className='info_name'>姓名：{userInfo && userInfo.userName}</p>

          <p className='flex_p'>
            <span> 联系方式：{userInfo && userInfo.telephone}</span>
            <span onClick={changephone}> &gt; </span>
          </p>

          <p className='flex_p'>
            <span> 修改密码</span>
            <span onClick={changepassword}> &gt; </span>
          </p>
          <span className='time_text'>
            最近修改时间:{userInfo && userInfo.modifyTime ? dayjs(userInfo.modifyTime).format("YYYY-MM-DD HH:mm:ss") : ''}</span>
        </div>
      </div>
    </div>
  )
}

import request from "@/services";

export const getTraceDataByTraceCodeId = (id: string, code?: string) => {
    const params: any = { id };
    if (code) {
        params.code = code;
    }
    return request({
        url: "/trace-code/trace/data",
        method: "get",
        params,
        // @ts-ignore
        __disableGlobalToast: true,
    });
};

export const getProductTraceDataByTraceCodeId = (id: string, code?: string) => {
    const params: any = { id };
    if (code) {
        params.code = code;
    }
    return request({
        url: "/trace-code/trace/product",
        method: "get",
        params,
    });
};

export const getInspectionTraceDataByTraceCodeId = (id: string, code?: string) => {
    const params: any = { id };
    if (code) {
        params.code = code;
    }
    return request({
        url: "/trace-code/trace/inspection",
        method: "get",
        params,
    });
};

export const getOrgTraceDataByTraceCodeId = (id: string, code?: string) => {
    const params: any = { id };
    if (code) {
        params.code = code;
    }
    return request({
        url: "/trace-code/trace/org",
        method: "get",
        params,
    });
};

export interface IGetTraceQueryRecordProps {
    codeId: number | string;
    pageIndex: number;
    pageSize: number;
}
export const getTraceQueryRecord = (props: IGetTraceQueryRecordProps) => {
    return request({
        url: "/trace-code/queryRecord",
        method: "post",
        data: props,
    });
};

export const getTransactionChainInfo = (transactionId: string) => {
    return request({
        url: "/trace-code/getChainHis",
        method: "get",
        params: {
            transactionId,
        },
    });
};
export const getFeekBackRequest = (props: any) => {
  return request({
      url: "/score/add",
      method: "post",
      data: props,
  });
};

// 新增接口：检查是否需要显示输入页面
export const checkNeedInputPage = (id: string) => {
    return request({
        url: "/trace-code/trace/check",
        method: "get",
        params: {
            id,
        },
    });
};

// 检查防伪码是否正确
export const verifyCode = (params: { id: string; code: string }) => {
  return request({
      url: "/trace-code/trace/verifyCode",
      method: "get",
      params: {
        id: params.id,
        code: params.code
      },
  });
};

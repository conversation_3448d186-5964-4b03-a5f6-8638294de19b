import { LeftOutline } from "antd-mobile-icons";
import { useTitle } from "ahooks";
import { useNavigate } from "react-router-dom";

import { useBackRoute } from "@/hooks";

import styles from "./index.module.less";

interface INavBarProps {
    backArrow?: boolean;
    onBack?: () => void;
    children?: string;
    color?: React.CSSProperties["color"];
}
export default ({
    children = "",
    onBack,
    backArrow = true,
    color,
}: INavBarProps) => {
    const navigate = useNavigate();
    const handleBack = useBackRoute();

    useTitle(children, {
        restoreOnUnmount: true,
    });

    return (
        <div
            className={styles.navBar}
            style={{
                color: color,
            }}
        >
            {backArrow && (
                <div
                    className="navBar__backArrow"
                    onClick={() => {
                        onBack ? onBack() : handleBack();
                    }}
                >
                    <LeftOutline
                        style={{
                            fontSize: "var(--text-md)",
                        }}
                    />
                </div>
            )}
            <div className="navBar__title">{children}</div>
        </div>
    );
};

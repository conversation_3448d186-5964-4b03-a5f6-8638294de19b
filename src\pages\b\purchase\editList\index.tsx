import { useState, useRef, RefObject, useEffect, useMemo } from "react";
import { useNavigate, useParams, useOutletContext } from "react-router-dom";
import {
    Button,
    Input,
    TextArea,
    Picker,
    InfiniteScroll,
    Toast,
    ErrorBlock,
    DotLoading,
    PullToRefresh,
    Space,
    DatePicker,
    Stepper,
} from "antd-mobile";

import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
    ScanningOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import type { DatePickerRef } from "antd-mobile/es/components/date-picker";
import { useRequest, useInfiniteScroll, useDebounce } from "ahooks";
import useUrlState from "@ahooksjs/use-url-state";
import request from "@/services";
import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";

import { INFINITE_LIST_PAGE_SIZE } from "@/config";
import { bSideRequests } from "@/services";
import ScanQRCode from "./qrcode";
import styles from "./index.module.less";
import {
    showToastLoading,
    requestDataSign,
    ToastLoadingHandler,
    rsaEncrypt,
} from "@/utils";

import type { PickerColumnItem } from "antd-mobile/es/components/picker-view";

interface CustomPickerColumnItem extends PickerColumnItem {
    disabled?: boolean; // 扩展 PickerColumnItem，增加 disabled 属性
}

export default () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const { setPageTitle, setEditPageState, editPageState } =
        useOutletContext<any>();
    const [visible, setVisible] = useState(false);
    const [newVisible, setnewVisible] = useState(false);
    const [repeatVisible, setrepeatVisible] = useState(false);

    const [value, setValue] = useState<(string | null)[]>(["M"]);
    const [disabled, setDisabled] = useState(true);
    const [state, setState] = useState(0);
    const [status, setStatus] = useState("");

    const [selectedLandBatch, setSelectedLandBatch] = useState(true);
    const [landTypeValue, setLandTypeValue] = useState(undefined);
    const [landplantBatchValue, setLandplantBatchValue] = useState(undefined);
    const [editPurchaseBatch, setEditPurchaseBatch] = useState("");
    const [farmerN, setFarmerN] = useState("");
    const [purchaseBatchCount, setPurchaseBatchCount] = useState("");
    const [myFormRef] = Form.useForm();
    const [isPickerDisabledlandType, setIsPickerDisabledlandType] =
        useState(null);

    const [isPickerDisabledplantBatch, setIsPickerDisabledplantBatch] =
        useState(undefined);
    const [istext, setIstext] = useState<any>("");
    // const myFormRef = useRef<any>(null);
    const formValChange = () => {
        console.log(myFormRef);

        const formInfo = myFormRef.getFieldsValue();
        // let formInfo = myFormRef.current.getFieldsValue();
        console.log(formInfo, "info");
        // if (
        //     myFormRef.current &&
        //     formInfo.newPassword &&
        //     formInfo.oldPassword &&
        //     formInfo.repeatPassword
        // ) {
        //     myFormRef.current.validateFields();
        // }
    };

    const back = () => {
        navigate("/b/personalinfo");
    };

    const ok = async (values: any) => {
        console.log(values);
        try {
            const pKRet = await request({
                method: "get",
                url: "/sys-config/getPublicKey",
                data: {},
            });
            const pK = pKRet?.data?.data || "";
            const requestData = {
                // farmerName:
                //       values.processImg &&
                //       values.processImg.map((item: any) => item.value),
                //   processInstructions: values.processInstructions,
                id: id,
                farmerName: values.farmerName,
                landName: values.landName[0],
                phoneNumber: values.phoneNumber,
                plantBatch: values.plantBatch[0],
                landType: values.landType[0],
                purchaseTimes: values.purchaseTimes,
                purchaseBatch: values.purchaseBatch,
                purchaseWeight: values.purchaseWeight,
                purchaseUnitPrice: values.purchaseUnitPrice,
                purchaseTime: values.DatePicker,
                bagCount: values.bagCount,
                // processVideo: values?.processVideo?.[0]?.value,
            } as const;
            const signedData = await requestDataSign(
                requestData,
                "modifyMaterialPurchaseVo",
            );
            toastLoadingHandlerRef.current = showToastLoading({
                duration: 0,
                content: "正在上传",
            });
            productiveProcessEntryRequest.run(signedData);
        } catch (err: any) {
            console.log(err, "err");
            Toast.show({
                icon: "fail",
                content: err?.toString?.() || "加密失败",
            });
        }
    };

    const cancel = (e) => {
        setIstext(e);
        deprecateProductiveProcessRequest.run({
            id: id,
            opt: e,
        });
    };

    // const handleInput1Change = (e) => {
    //     setInput1Value(e.target.value);
    // };

    // const handleInput2Change = (e) => {
    //     setInput2Value(e.target.value);
    // };

    const handleInput1Blur = async () => {
        const pKRet = await request({
            method: "get",
            url: "/sys-config/getPublicKey",
            data: {},
        });
        const pK = pKRet?.data?.data || "";
        // setInput1Focused(false);
        if (
            myFormRef.getFieldsValue().phoneNumber &&
            myFormRef.getFieldsValue().farmerName
        ) {
            purchaseCont.run({
                farmerName: myFormRef.getFieldsValue().farmerName,
                phoneNumber: await rsaEncrypt(
                    myFormRef.getFieldsValue().phoneNumber,
                    pK,
                ),
            });

            myFormRef.setFieldsValue({
                purchaseTimes: purchaseCont.data?.data?.data,
            });
        }
    };

    const handleInput2Blur = async () => {
        const pKRet = await request({
            method: "get",
            url: "/sys-config/getPublicKey",
            data: {},
        });
        const pK = pKRet?.data?.data || "";
        // setInput1Focused(false);
        if (
            myFormRef.getFieldsValue().phoneNumber &&
            myFormRef.getFieldsValue().farmerName
        ) {
            purchaseCont.run({
                farmerName: myFormRef.getFieldsValue().farmerName,
                phoneNumber: await rsaEncrypt(
                    myFormRef.getFieldsValue().phoneNumber,
                    pK,
                ),
            });
            myFormRef.setFieldsValue({
                purchaseTimes: purchaseCont.data?.data?.data,
            });
        }
    };
    const selectValue = (e) => {
        // console.log(myFormRef.getFieldsValue().farmerInfo, "111111111");
        console.log(e, "eeeeeeeeeeeeee");
    };

    const pickerReflandName = useRef(null); // 创建一个ref来引用Picker实例
    const pickerReflandType = useRef(null); // 创建一个ref来引用Picker实例
    const pickerRefplantBatch = useRef(null); // 创建一个ref来引用Picker实例

    const handlePickerlandName = (selectedValues: any) => {
        console.log(selectedValues, "111111111111111111111111111111111");
        // 更新Form的字段值
        myFormRef.setFieldsValue({ landName: selectedValues });

        plantNameByLandId.run({ landId: selectedValues[0] });

        myFormRef.setFieldsValue({ landType: undefined });
        myFormRef.setFieldsValue({ plantBatch: undefined });
        setIsPickerDisabledlandType(selectedValues);
        setLandTypeValue(undefined);
        setLandplantBatchValue(undefined);
        setIsPickerDisabledplantBatch(undefined);
    };

    const handlePickerConfirm = (selectedValues: any) => {
        // 更新Form的字段值
        // myFormRef.setFieldsValue({ landType: selectedValues });
        myFormRef.setFieldsValue({ landType: selectedValues });
        LandIdAndPlantName.run({
            landId: myFormRef.getFieldsValue().landName[0],
            plantName: selectedValues[0],
        });
        setIsPickerDisabledplantBatch(selectedValues);
        myFormRef.setFieldsValue({ plantBatch: undefined });

        setLandTypeValue(selectedValues);
    };

    const handlePickerplantBatch = (selectedValues: any, data: any) => {
        const selectedItem = data.items.find(
            (item) => item.value === selectedValues[0],
        );
        if (!selectedItem.disabled) {
            // 更新Form的字段值
            myFormRef.setFieldsValue({ plantBatch: selectedValues });
            setLandplantBatchValue(selectedValues);
        } else {
            // 如果选择了被禁用的项，可以选择不执行任何操作，或者给出提示
            Toast.show("当前种植批次已被禁用！");
        }
        // LandIdAndPlantName.run({
        //     landId: myFormRef.getFieldsValue().landName[0],
        //     plantName: selectedValues[0],
        // });
    };
    // const handlePickerConfirm = (selectedValues: any) => {
    //     // 更新Form的字段值
    //     myFormRef.setFieldsValue({ type: selectedValues });
    // };
    // 编辑
    // const editDetail = () => {
    //     if (!firstlogin) {
    //         setDisabled(true);
    //         Toast.show({
    //             // icon: "error",
    //             content: "该收购批次已在加工过程中使用，不可修改",
    //         });
    //     } else {
    //         setDisabled(false);
    //     }
    // };

    const editDetail = () => {
        // editPurchaseBatch
        dePurchaseBatchExistsRequest.run({
            purchaseBatch: editPurchaseBatch,
        });
        // setDisabled(false);
        // if (!firstlogin) {
        //     setDisabled(true);
        //     Toast.show({
        //         // icon: "error",
        //         content: "该收购批次已在加工过程中使用，不可修改",
        //     });
        // } else {
        //     setDisabled(false);
        // }
    };
    const [isFormFinished, setIsFormFinished] = useState(true);
    const [firstlogin, setFirstlogin] = useState(false);
    const toastLoadingHandlerRef = useRef<ToastLoadingHandler | null>(null);
    const productiveProcessEntryRequest = useRequest(
        bSideRequests.purchasePageEdit,
        {
            manual: Boolean(selectedLandBatch),
            onSuccess() {
                toastLoadingHandlerRef?.current?.close?.();
                setIsFormFinished(true);
                Toast.show("操作成功");
                myFormRef.resetFields();
                navigate("/b/home/<USER>/log");
            },
        },
    );
    // 获取农户名称列表
    const LandSourceService = useRequest(bSideRequests.LandSourceService, {
        manual: false,
        onSuccess() {
            // toastLoadingHandlerRef?.current?.close?.();
            // setIsFormFinished(true);
            // Toast.show("上报成功");
            // myFormRef.resetFields();
        },
    });
    const options = LandSourceService?.data?.data?.data?.map((obj) => ({
        label: obj.landName,
        value: obj.landId,
    }));
    console.log(options);
    // console.log(options);

    // 农作物类型

    const plantNameByLandId = useRequest(bSideRequests.plantNameByLandId, {
        manual: true,
        onSuccess() {
            // toastLoadingHandlerRef?.current?.close?.();
            // setIsFormFinished(true);
            // Toast.show("上报成功");
            // myFormRef.resetFields();
            // myFormRef.setFieldsValue({
            //     landType: ["大米2号"],
            //     // bagCount: result.bagCount,
            // });
        },
    });
    const optionslandType = plantNameByLandId?.data?.data?.data?.map((obj) => ({
        label: obj.plantName,
        value: obj.plantName,
    }));
    console.log(optionslandType, "optionslandType");
    // 种植批次
    const LandIdAndPlantName = useRequest(bSideRequests.LandIdAndPlantName, {
        manual: true,
        onSuccess() {
            // toastLoadingHandlerRef?.current?.close?.();
            // setIsFormFinished(true);
            // Toast.show("上报成功");
            // myFormRef.resetFields();
        },
    });
    const optionsplantBatch = LandIdAndPlantName?.data?.data?.data?.map(
        (obj) => ({
            label: obj.plantBatch,
            value: obj.plantBatch,
            disabled: obj?.state === 1,
        }),
    );

    const renderItem = (item: CustomPickerColumnItem) => {
        if (item.disabled) {
            // 使用不同的样式来模拟置灰效果
            return (
                <div style={{ color: "#ccc", opacity: 0.5 }}>{item.label}</div>
            );
        }
        return <div>{item.label}</div>;
    };

    // 获取被收购次数
    const purchaseCont = useRequest(bSideRequests.purchaseCont, {
        manual: true,
        onSuccess(res: any) {
            // toastLoadingHandlerRef?.current?.close?.();
            // setIsFormFinished(true);
            // Toast.show("上报成功");
            // myFormRef.resetFields();
            myFormRef.setFieldsValue({
                purchaseTimes: res.data?.data,
            });
        },
    });
    //  详情和作废

    const getProductiveProcessDetailRequest = useRequest(
        () => {
            if (!id) {
                throw new Error("缺少id");
            }
            return bSideRequests.purchasePageDetail(id);
        },
        {
            async onSuccess(res: any) {
                console.log("ssss===", res);
                const result = res?.data?.data;
                setEditPurchaseBatch(result.purchaseBatch);
                const isoDateString = new Date(
                    dayjs(result.purchaseTime).format("YYYY-MM-DD HH:mm"),
                );
                plantNameByLandId.run({
                    landId: result.landId,
                });

                LandIdAndPlantName.run({
                    landId: result.landId,
                    plantName: result.plantName,
                });
                setLandTypeValue(result);
                setIsPickerDisabledplantBatch(result);
                setLandplantBatchValue(result);
                setIsPickerDisabledlandType(result);
                // setIsPickerDisabledplantBatch()
                setState(result.state);
                setFarmerN(result.farmerName);
                setPurchaseBatchCount(result.purchaseBatch);
                console.log(result.landId);
                myFormRef.setFieldsValue({
                    farmerName: result.farmerName,
                    phoneNumber: result.phoneNumber,
                    purchaseTimes: result.materialPurchaseCount,
                    landName: [+result.landId],
                    landType: [result.plantName],
                    purchaseWeight: result.purchaseWeight,
                    purchaseUnitPrice: result.purchaseUnitPrice,
                    purchaseBatch: result.purchaseBatch,
                    plantBatch: [result.plantBatch],
                    bagCount: result.bagCount,
                    DatePicker: isoDateString,
                    // bagCount: result.bagCount,
                });

                // const videos = await decryptedUrl(result?.processVideo);
                // const arrayData = await Promise.all(
                //     isArrayArr(result?.processImg)?.map((item: any) => {
                //         return decryptedUrl(item);
                //     }),
                // );
                // const image = await decryptedUrl(result?.productImg)
                // setVideo(videos);
                // setPromotionPicData(arrayData);
            },
            refreshDeps: [id],
        },
    );

    useEffect(() => {
        const productionId = sessionStorage.getItem("productionId");
        setFirstlogin(productionId == "null");
    }, []);

    // 监听 disabled 状态变化，传递给父组件
    useEffect(() => {
        if (setEditPageState) {
            setEditPageState({ disabled, id });
        }
    }, [disabled, id, setEditPageState]);

    // 监听返回到禁用状态事件
    useEffect(() => {
        const handleBackToDisabled = () => {
            console.log("接收到返回禁用状态事件");
            if (!disabled) {
                // 从编辑状态返回到禁用状态
                setDisabled(true);
            }
        };

        window.addEventListener("backToDisabled", handleBackToDisabled);

        return () => {
            window.removeEventListener("backToDisabled", handleBackToDisabled);
        };
    }, [disabled]);
    // sessionStorage.getItem("productionId") == "null"
    //     ? setFirstlogin(true)
    //     : setFirstlogin(false);

    // 编辑
    const dePurchaseBatchExistsRequest = useRequest(
        bSideRequests.PurchaseBatchExists,
        {
            manual: true,
            onSuccess(res: any) {
                if (res?.data.code === 200) {
                    setDisabled(false);
                } else {
                    Toast.show({
                        icon: "error",
                        content: res?.data.message,
                    });
                }
                // Toast.show({
                //     icon: "success",
                //     content: "编辑成功",
                // });
                // getProductiveProcessDetailRequest.refresh();
                // myFormRef.resetFields();
                // navigate("/b/home/<USER>/log");
            },
        },
    );
    const deprecateProductiveProcessRequest = useRequest(
        bSideRequests.purchaseUpdata,
        {
            manual: true,
            onSuccess(res) {
                console.log(res);

                // toastLoadingHandlerRef?.current?.close?.();
                // setIsFormFinished(true);
                // Toast.show("上报成功");
                // myFormRef.resetFields();
                // if (opt) { }
                console.log(istext);

                Toast.show({
                    icon: "success",
                    content: istext == "DISABLE" ? "禁用成功" : "启用成功",
                });
                getProductiveProcessDetailRequest.refresh();
                myFormRef.resetFields();
                navigate("/b/home/<USER>/log");
            },
        },
    );
    if (getProductiveProcessDetailRequest.loading) {
        return (
            <div
                style={{
                    paddingTop: "50px",
                    textAlign: "center",
                    color: "var(--adm-color-weak)",
                }}
            >
                <div style={{ fontSize: 24, marginBottom: 24 }}>
                    <DotLoading />
                </div>
                正在加载数据
            </div>
        );
    }
    const validateNoSpacesName = (rule: any, value: any) => {
        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,30}$/);
        const verify = regExp.test(value);

        if (!value) {
            return Promise.reject("请输入农户姓名");
        } else if (value[0] == " " || value[value.length - 1] == " ") {
            return Promise.reject("字段前后不能输入空格！");
        } else if (verify === false) {
            if (value.length > 30) {
                return Promise.reject("请保持字符在30字符以内");
            } else {
                return Promise.reject("请输入农户姓名，支持中文、字母或数字!");
            }
        } else {
            return Promise.resolve();
        }
    };
    const validateNoSpacesBatch = (rule: any, value: any) => {
        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,50}$/);
        const verify = regExp.test(value);

        if (!value) {
            return Promise.reject("请输入收购批次");
        } else if (value[0] == " " || value[value.length - 1] == " ") {
            return Promise.reject("字段前后不能输入空格！");
        } else if (verify === false) {
            if (value.length > 30) {
                return Promise.reject("请保持字符在50字符以内");
            } else {
                return Promise.reject("请输入收购批次，支持中文、字母或数字!");
            }
        } else {
            return Promise.resolve();
        }
    };
    return (
        <div className={`${styles.logList}`}>
            <div className={`${styles.result}`}>
                <div style={{ fontSize: "16.8px" }}>&nbsp;&nbsp;农户信息</div>

                {/* <div>
                    {codeResultShow ? (
                        <div className={`${styles.resultView}`}>
                            <ScanQRCode setCodeResult={setCodeResult} />
                        </div>
                    ) : (
                        <div onClick={CodeResult}>
                            <ScanningOutline /> <a>扫码录入</a>
                        </div>
                    )}
                </div> */}
            </div>
            <Form
                layout="horizontal"
                onFinish={ok}
                form={myFormRef}
                onValuesChange={formValChange}
            >
                {/* <Form.Item
                    name="farmerInfo"
                    label="农户信息"
                    rules={[{ required: true, message: "农户信息" }]}
                >

                    {
                        codeResultShow ? (
                            <div className={`${styles.result}`}>
                                <ScanQRCode setCodeResult={setCodeResult} />
                            </div>
                        ) : (
                            <div onClick={CodeResult}>
                                <ScanningOutline /> <a>扫码录入</a>
                            </div>
                        )
                    }
                </Form.Item> */}

                <Form.Item
                    name="farmerName"
                    label="农户姓名"
                    disabled={true}
                    rules={[
                        { required: true, message: "" },
                        { validator: validateNoSpacesName },
                    ]}
                >
                    <div
                        style={{
                            width: "100px",
                            maxWidth: "100px",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "nowrap",
                            textAlign: "right",
                            marginLeft: "auto",
                            fontSize: "14px",
                        }}
                    >
                        {farmerN}
                    </div>
                    {/* <Input
                        style={{
                            "--text-align": "right",
                            width: "180px",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
           80px             whiteSpace: "nowrap",
                        }}
                        placeholder="请填写"
                        onBlur={handleInput1Blur}
                    /> */}
                </Form.Item>
                <Form.Item
                    name="phoneNumber"
                    label="联系方式"
                    disabled={true}
                    rules={[
                        {
                            required: true,
                            message: "联系方式不能为空",
                        },
                        {
                            pattern: /^[1][3,4,5,6.7,8,9][0-9]{9}$/,
                            message: "请输入正确的联系方式",
                        },
                    ]}
                >
                    <Input
                        style={{ "--text-align": "right" }}
                        // onChange={handleInput2Change}
                        placeholder="请填写"
                        onBlur={handleInput2Blur}
                    />
                </Form.Item>
                <Form.Item
                    name="purchaseTimes"
                    label="被收购次数"
                    // rules={[{ required: true, message: "农户信息" }]}
                    disabled={true}
                >
                    <Input
                        style={{ "--text-align": "right" }}
                        placeholder=""
                        disabled={true}
                    />
                </Form.Item>
                <Form.Item name="name" label="收购信息"></Form.Item>
                <Form.Item
                    name="landName"
                    label="地块名称"
                    disabled={disabled}
                    rules={[{ required: true, message: "请选择地块名称" }]}
                >
                    <Picker
                        ref={pickerReflandName}
                        columns={options ? [options] : []}
                        onConfirm={handlePickerlandName}
                        getContainer={() => document.getElementById("root")}
                        // value={valueselct}
                        // defaultValue={[10]}
                    >
                        {(items, { open }) => (
                            <span
                                onClick={open}
                                className={`${styles.spanright}`}
                            >
                                {items[0]?.label || "请选择"}
                            </span>
                        )}
                    </Picker>
                </Form.Item>
                <Form.Item
                    name="landType"
                    label="农作物类型"
                    rules={[{ required: true, message: "请选择农作物类型" }]}
                    disabled={disabled}
                >
                    <Picker
                        ref={pickerReflandType}
                        columns={optionslandType ? [optionslandType] : []}
                        onConfirm={handlePickerConfirm}
                        getContainer={() => document.getElementById("root")}
                        // value={landTypeValue}
                        // defaultValue={["Mon"]}
                    >
                        {(items, { open }) =>
                            isPickerDisabledlandType ? (
                                <span
                                    onClick={open}
                                    className={`${styles.spanright}`}
                                >
                                    {landTypeValue
                                        ? items[0]?.label
                                            ? items[0]?.label
                                            : "请选择"
                                        : "请选择"}
                                </span>
                            ) : (
                                <span
                                    style={{ color: "#909090" }}
                                    className={`${styles.spanright}`}
                                >
                                    {items[0]?.label || "请选择"}
                                </span>
                            )
                        }
                    </Picker>
                </Form.Item>

                <Form.Item
                    name="plantBatch"
                    label="种植批次"
                    disabled={disabled}
                    rules={[{ required: true, message: "请选择种植批次" }]}
                >
                    <Picker
                        ref={pickerRefplantBatch}
                        columns={optionsplantBatch ? [optionsplantBatch] : []}
                        onConfirm={handlePickerplantBatch}
                        value={landplantBatchValue}
                        renderLabel={renderItem}
                        getContainer={() => document.getElementById("root")}
                    >
                        {(items, { open }) =>
                            isPickerDisabledplantBatch ? (
                                <span
                                    onClick={open}
                                    className={`${styles.spanright}`}
                                >
                                    {landplantBatchValue
                                        ? items[0]?.label
                                            ? items[0]?.label
                                            : "请选择"
                                        : "请选择"}
                                </span>
                            ) : (
                                <span
                                    style={{ color: "#909090" }}
                                    className={`${styles.spanright}`}
                                >
                                    {"请选择"}
                                </span>
                            )
                        }
                    </Picker>
                    {/* <Input onChange={console.log} placeholder="请输入姓名" /> */}
                </Form.Item>

                <Form.Item
                    name="purchaseBatch"
                    label="收购批次"
                    rules={[
                        { required: true, message: "" },
                        { validator: validateNoSpacesBatch },
                    ]}
                    disabled={disabled}
                >
                    {disabled ? (
                        <div
                            style={{
                                width: "100%",
                                display: "flex",
                                justifyContent: "flex-end",
                                alignItems: "center",
                            }}
                        >
                            <div
                                style={{
                                    maxWidth: "200px",
                                    overflow: "hidden",
                                    textOverflow: "ellipsis",
                                    whiteSpace: "nowrap",
                                    textAlign: "right",
                                }}
                            >
                                {purchaseBatchCount}
                            </div>
                        </div>
                    ) : (
                        <Input
                            style={{
                                "--text-align": "right",
                                paddingRight: "10px",
                            }}
                            onChange={console.log}
                            placeholder="请填写"
                            disabled={disabled}
                        />
                    )}
                </Form.Item>
                <Form.Item
                    name="DatePicker"
                    label="收购时间"
                    trigger="onConfirm"
                    disabled={disabled}
                    rules={[{ required: true, message: "请选择收购时间" }]}
                    onClick={(e, datePickerRef: RefObject<DatePickerRef>) => {
                        datePickerRef.current?.open();
                    }}
                >
                    <DatePicker
                        precision="second"
                        getContainer={() => document.getElementById("root")}
                    >
                        {(value) =>
                            value
                                ? dayjs(value).format("YYYY-MM-DD HH:mm:ss")
                                : "请选择收购时间"
                        }
                    </DatePicker>
                </Form.Item>

                <Form.Item
                    name="purchaseWeight"
                    label="收购重量"
                    extra={<div>吨</div>}
                    disabled={disabled}
                    rules={[
                        {
                            required: true,
                            message: "请输入收购重量",
                            min: 0.01,
                            type: "number",
                        },
                    ]}
                >
                    <Stepper
                        style={{ "--input-width": "68px" }}
                        digits={2}
                        min={0}
                        max={100000}
                        step={0.01}
                        precision={2}
                    />
                </Form.Item>

                <Form.Item
                    name="bagCount"
                    label="装袋数量"
                    disabled={disabled}
                    extra={<div>袋</div>}
                    rules={[
                        {
                            required: true,
                            message: "请输入装袋数量",
                            min: 0.1,
                            type: "number",
                        },
                    ]}
                >
                    <Stepper
                        style={{ "--input-width": "68px" }}
                        digits={1}
                        min={0}
                        max={10000000}
                        step={0.1}
                        precision={1}
                    />
                </Form.Item>

                <Form.Item
                    name="purchaseUnitPrice"
                    label="收购单价"
                    disabled={disabled}
                    extra={<div>元/吨</div>}
                    rules={[
                        {
                            required: true,
                            message: "请输入收购单价",
                            min: 0.01,
                            type: "number",
                        },
                    ]}
                >
                    <Stepper
                        style={{ "--input-width": "68px" }}
                        digits={2}
                        min={0}
                        max={100000}
                        step={0.01}
                        precision={2}
                    />
                </Form.Item>
                <div className={`${styles.flexbtn}`}>
                    {state == 0 ? (
                        <>
                            {disabled ? (
                                <div
                                    className={`${styles.BtnEdit}`}
                                    onClick={() => cancel("DISABLE")}
                                >
                                    禁用
                                </div>
                            ) : (
                                <></>
                            )}
                            {/* <Button
                    color="primary"
                    fill="none"
                    className={`${styles.Btn}`}
                    onClick={() => cancel("DISABLE")}
                  >
                    禁用
                  </Button> */}
                            {disabled ? (
                                <div
                                    onClick={editDetail}
                                    className={`${styles.BtnEdit}`}
                                >
                                    编辑
                                </div>
                            ) : (
                                <Button
                                    color="primary"
                                    fill="none"
                                    type="submit"
                                    className={`${styles.okBtn}`}
                                >
                                    确认
                                </Button>
                            )}
                        </>
                    ) : (
                        <Button
                            color="primary"
                            fill="none"
                            onClick={() => cancel("ENABLE")}
                            className={`${styles.Enable}`}
                        >
                            启用
                        </Button>
                    )}
                </div>
            </Form>
        </div>
    );
};

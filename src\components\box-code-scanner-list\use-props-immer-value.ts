// 修改自 https://github.com/ant-design/ant-design-mobile/blob/master/src/utils/use-props-value.ts#L10
import { SetStateAction, useRef } from 'react'
import { useMemoizedFn, useUpdate } from 'ahooks'
import { produce } from 'immer'
import type { DraftFunction } from 'use-immer'

type Options<T> = {
    value?: T
    defaultValue: T
    onChange?: (v: T) => void
}

function usePropsImmerValue<T>(options: Options<T>) {
    const { value, defaultValue, onChange } = options

    const update = useUpdate()

    const stateRef = useRef<T>(value !== undefined ? value : defaultValue)
    if (value !== undefined) {
        stateRef.current = value
    }

    const setState = useMemoizedFn(
        (v: T | DraftFunction<T>, forceTrigger: boolean = false) => {
            // `forceTrigger` means trigger `onChange` even if `v` is the same as `stateRef.current`
            const nextValue =
                typeof v === 'function'
                    ? produce(stateRef.current, v as DraftFunction<T>)
                    : v
            if (!forceTrigger && nextValue === stateRef.current) return
            stateRef.current = nextValue
            update()
            return onChange?.(nextValue)
        }
    )
    return [stateRef.current, setState] as const
}

export default usePropsImmerValue
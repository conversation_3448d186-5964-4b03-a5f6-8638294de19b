import { List as AntdList } from "antd-mobile";
import { LeftOutline } from "antd-mobile-icons";
import { useNavigate } from "react-router-dom";

import styles from "./index.module.less";

import type { ListProps } from "antd-mobile";

interface IListProps extends ListProps {}
/**
 * docs: https://mobile.ant.design/zh/components/list
 */
const List = (props: IListProps) => {
    return (
        <div className={styles.list}>
            <AntdList {...props}></AntdList>
        </div>
    );
};
List.Item = AntdList.Item;

export default List;

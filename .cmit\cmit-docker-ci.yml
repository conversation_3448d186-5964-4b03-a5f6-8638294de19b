name: Docker Image CI

on:
    push:
        branches: ["dev"]
    pull_request:
        branches: ["dev"]

jobs:
    build:
        name: cmit-docker-ci
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v2
            - name: set env TAG
              run: echo "TAG=$(date +%s)" >> $GITHUB_ENV
            - name: docker login
              run: docker login artifactory.dep.devops.cmit.cloud:20101 -u ${{env.USER_NAME}} -p ${{env.PASSWORD}}
            - name: Build the Docker image
              run: docker build . --file ${{env.PROJECT_PATH}}/panzhou-ci.Dockerfile --label commit-id=${{env.GIT_COMMIT_ID}} --label branch=${{env.GIT_BRANCH}} --tag artifactory.dep.devops.cmit.cloud:20101/application_service_development/cm_ricetrace_h5:${{env.GIT_COMMIT_ID}} --build-arg PROJECT_PATH=${{env.PROJECT_PATH}}
            - name: docker push
              run: docker push artifactory.dep.devops.cmit.cloud:20101/application_service_development/cm_ricetrace_h5:${{env.GIT_COMMIT_ID}}
            - name: Remove local docker image
              run: docker image rm -f artifactory.dep.devops.cmit.cloud:20101/application_service_development/cm_ricetrace_h5:${{env.GIT_COMMIT_ID}}

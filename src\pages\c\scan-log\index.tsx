import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
    Button,
    Input,
    Text<PERSON><PERSON>,
    Picker,
    InfiniteScroll,
    Toast,
    ErrorBlock,
    DotLoading,
    PullToRefresh,
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import { useRequest, useInfiniteScroll, useDebounce } from "ahooks";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";

import { INFINITE_LIST_PAGE_SIZE } from "@/config";
import { cSideRequests } from "@/services";

import styles from "./index.module.less";

const ScanLog = () => {
    const navigate = useNavigate();
    const { traceCodeId } = useParams();

    const getTraceQueryRecordListRequest = useInfiniteScroll((d) => {
        if (!traceCodeId) {
            return Promise.reject();
        }
        const page = d
            ? Math.ceil(d.list.length / INFINITE_LIST_PAGE_SIZE) + 1
            : 1;
        return cSideRequests
            .getTraceQueryRecord({
                pageIndex: page,
                pageSize: INFINITE_LIST_PAGE_SIZE,
                codeId: traceCodeId,
            })
            .then((res) => {
                const listInfo = res?.data?.data || {};
                return {
                    list: listInfo?.records || [],
                    total: listInfo?.total || 0,
                };
            });
    }, {});
    const hasMore =
        getTraceQueryRecordListRequest.data &&
        getTraceQueryRecordListRequest.data.list.length <
            getTraceQueryRecordListRequest.data.total;

    const data = getTraceQueryRecordListRequest?.data?.list || [];

    const renderList = () => {
        if (getTraceQueryRecordListRequest.loading) {
            return (
                <div
                    style={{
                        paddingTop: "30px",
                        textAlign: "center",
                        color: "var(--adm-color-weak)",
                    }}
                >
                    <div style={{ fontSize: 24, marginBottom: 24 }}>
                        <DotLoading />
                    </div>
                    正在加载数据
                </div>
            );
        }
        if (data.length === 0) {
            return (
                <ErrorBlock
                    image={<div></div>}
                    title="暂无数据"
                    description=""
                />
            );
        }

        return (
            <div>
                <PullToRefresh
                    onRefresh={async () => {
                        await getTraceQueryRecordListRequest.reloadAsync();
                    }}
                >
                    <List
                        className="packLogList"
                        header={
                            <div className="scanTime">
                                共计查询
                                {getTraceQueryRecordListRequest?.data?.total ||
                                    "--"}
                                次
                            </div>
                        }
                    >
                        {data.map((item, index) => {
                            return (
                                <List.Item
                                    key={item?.id}
                                    extra={
                                        item
                                            ? dayjs(item).format(
                                                  "YYYY-MM-DD HH:mm:ss",
                                              )
                                            : "-"
                                    }
                                >
                                    第{index + 1}次查询
                                </List.Item>
                            );
                        })}
                    </List>
                </PullToRefresh>
                <InfiniteScroll
                    loadMore={async () => {
                        await getTraceQueryRecordListRequest.loadMoreAsync();
                    }}
                    hasMore={hasMore || false}
                />
            </div>
        );
    };

    return (
        <div className={`_global_page ${styles.scanLog}`}>
            <NavBar>查询记录</NavBar>
            <div className="_global_pageScrollContent">
                <div className="content">{renderList()}</div>
            </div>
        </div>
    );
};

export default ScanLog;

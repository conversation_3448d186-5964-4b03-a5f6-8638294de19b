import { useState, useRef, RefObject, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
    Button,
    Input,
    Text<PERSON><PERSON>,
    Picker,
    InfiniteScroll,
    Toast,
    ErrorBlock,
    DotLoading,
    PullToRefresh,
    Space,
    DatePicker,
    Stepper,
    Modal,
} from "antd-mobile";

import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
    ScanningOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import type { DatePickerRef } from "antd-mobile/es/components/date-picker";
import { useRequest, useInfiniteScroll, useDebounce } from "ahooks";
import useUrlState from "@ahooksjs/use-url-state";
import request from "@/services";
import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";

import { INFINITE_LIST_PAGE_SIZE } from "@/config";
import { bSideRequests } from "@/services";
import styles from "./index.module.less";
import copyToClipboard from "copy-to-clipboard";
import {
    showToastLoading,
    requestDataSign,
    ToastLoadingHandler,
    rsaEncrypt,
    randomPassword,
} from "@/utils";
import type { PickerColumnItem } from "antd-mobile/es/components/picker-view";
import { useConfirmBlock, useScanQRCode } from "@/hooks";

interface CustomPickerColumnItem extends PickerColumnItem {
    disabled?: boolean; // 扩展 PickerColumnItem，增加 disabled 属性
}

export default () => {
    const navigate = useNavigate();
    const [visible, setVisible] = useState(false);
    const [newVisible, setnewVisible] = useState(false);
    const [repeatVisible, setrepeatVisible] = useState(false);

    const [selectedLandBatch, setSelectedLandBatch] = useState(true);
    const [landTypeValue, setLandTypeValue] = useState(undefined);
    const [landplantBatchValue, setLandplantBatchValue] = useState(undefined);

    const [value, setValue] = useState<(string | null)[]>(["M"]);
    const [myFormRef] = Form.useForm();
    const [isPickerDisabledlandType, setIsPickerDisabledlandType] =
        useState(null);

    const [isPickerDisabledplantBatch, setIsPickerDisabledplantBatch] =
        useState(null);

    const { qrCodeScanElement, startScanQrCode, closeScanQrCode } =
        useScanQRCode();
    // const myFormRef = useRef<any>(null);
    const formValChange = () => {
        console.log(myFormRef);

        const formInfo = myFormRef.getFieldsValue();
        // let formInfo = myFormRef.current.getFieldsValue();
        console.log(formInfo, "info");
        // if (
        //     myFormRef.current &&
        //     formInfo.newPassword &&
        //     formInfo.oldPassword &&
        //     formInfo.repeatPassword
        // ) {
        //     myFormRef.current.validateFields();
        // }
    };
    const [isFormFinished, setIsFormFinished] = useState(true);

    const toastLoadingHandlerRef = useRef<ToastLoadingHandler | null>(null);

    const [checkRegisterModal, setCheckRegisterModal] = useState(false);
    const [formValues, setFormValues] = useState({
        farmerName: "",
        phoneNumber: "",
    }); // 用于存储表单值
    const [canSubmit, setCanSubmit] = useState(false);

    const productiveProcessEntryRequest = useRequest(
        bSideRequests.getProducListAdd,
        {
            manual: Boolean(selectedLandBatch),
            onSuccess() {
                toastLoadingHandlerRef?.current?.close?.();
                setIsFormFinished(true);
                Toast.show("新增收购成功");
                myFormRef.resetFields();
                navigate("/b/home/<USER>/log");
            },
        },
    );
    // 获取农户名称列表
    const LandSourceService = useRequest(bSideRequests.LandSourceService, {
        manual: false,
        onSuccess() {
            // toastLoadingHandlerRef?.current?.close?.();
            // setIsFormFinished(true);
            // Toast.show("上报成功");
            // myFormRef.resetFields();
        },
    });
    const options = LandSourceService?.data?.data?.data.map((obj) => ({
        label: obj.landName,
        value: obj.landId,
    }));

    // 农作物类型

    const plantNameByLandId = useRequest(bSideRequests.plantNameByLandId, {
        manual: true,
        onSuccess() {
            // toastLoadingHandlerRef?.current?.close?.();
            // setIsFormFinished(true);
            // Toast.show("上报成功");
            // myFormRef.resetFields();
        },
    });
    const optionslandType = plantNameByLandId?.data?.data?.data.map((obj) => ({
        label: obj.plantName,
        value: obj.plantName,
    }));

    // 种植批次
    const LandIdAndPlantName = useRequest(bSideRequests.LandIdAndPlantName, {
        manual: true,
        onSuccess() {
            // toastLoadingHandlerRef?.current?.close?.();
            // setIsFormFinished(true);
            // Toast.show("上报成功");
            // myFormRef.resetFields();
        },
    });
    const optionsplantBatch = LandIdAndPlantName?.data?.data?.data.map(
        (obj) => ({
            label: obj.plantBatch,
            value: obj.plantBatch,
            disabled: obj?.state === 1,
        }),
    );

    const renderItem = (item: CustomPickerColumnItem) => {
        if (item.disabled) {
            // 使用不同的样式来模拟置灰效果
            return (
                <div style={{ color: "#ccc", opacity: 0.5 }}>{item.label}</div>
            );
        }
        return <div>{item.label}</div>;
    };

    // 获取被收购次数
    const purchaseCont = useRequest(bSideRequests.purchaseCont, {
        manual: true,
        onSuccess(res: any) {
            // console.log('purchaseContpurchaseContpurchaseContpurchaseCont', res.data.data);

            myFormRef.setFieldsValue({
                purchaseTimes: res.data.data,
            });
            // toastLoadingHandlerRef?.current?.close?.();
            // setIsFormFinished(true);
            // Toast.show("上报成功");
            // myFormRef.resetFields();
        },
    });

    // 取消注册确认弹框
    const handleCancelRegister = () => {
        setCanSubmit(false);
        Modal.clear();
    };
    // 取消复制弹框

    const handleCancelCop = () => {
        Modal.clear();
        handleEncryptedFormData(formValues);
    };
    // 判断农户是否注册账号
    const handleCheckRegister = useRequest(bSideRequests.checkRegister, {
        manual: true,
        onSuccess: (res: any) => {
            if (res.data.data === true) {
                handleEncryptedFormData(formValues);
            }
            if (res.data.data === false) {
                registerModal();
            }
            setCanSubmit(false);
        },
        onError: (err) => {
            setCanSubmit(false);
        },
    });

    // 保存注册
    const saveRegister = useRequest(bSideRequests.registerAccount, {
        manual: true,
        onSuccess(res: any) {
            console.log(res.data.data, "data");
            if (res.data.code === 200) {
                Modal.clear();
                passwordModal();
                setCanSubmit(false);
                // handleEncryptedFormData(formValues);
            }
        },
        onError(e, params) {
            setCanSubmit(false);
            Toast.show(e.message);
        },
    });
    const [createPassword, setCreatePassword] = useState("");
    // 提交注册
    const handleRegister = async () => {
        const pKRet = await request({
            method: "get",
            url: "/sys-config/getPublicKey",
            data: {},
        });
        const pK = pKRet?.data?.data || "";
        const randomPd = await randomPassword(8);
        setCreatePassword(randomPd);
        saveRegister.run({
            farmerName: formValues.farmerName,
            phoneNumber: await rsaEncrypt(formValues.phoneNumber, pK),
            password: await rsaEncrypt(randomPd, pK),
        });
    };

    const registerModal = () => {
        return Modal.show({
            title: "该农户尚未注册账号",
            showCloseButton: false,
            closeOnMaskClick: false,
            bodyClassName: "custom-modal-body",
            getContainer: () => document.getElementById("root"),
            content: (
                <div className="content">
                    <div className="title">
                        请确认农户信息是否正确，确认后系统将自动为该农户注册账号:{" "}
                    </div>
                    <div className="desc">
                        <div>农户姓名：{formValues?.farmerName}</div>
                        <div>联系方式：{formValues?.phoneNumber}</div>
                    </div>
                    <div className="action">
                        <span
                            className="cancel-btn"
                            onClick={handleCancelRegister}
                        >
                            取消
                        </span>
                        <span className="ok-btn" onClick={handleRegister}>
                            确认
                        </span>
                    </div>
                </div>
            ),
        });
    };

    // 密码复制
    const handleCopyAndClose = async () => {
        // navigator.clipboard.writeText("我是复制体");

        const copyRet = copyToClipboard(createPassword!);
        if (copyRet) {
            Toast.show("复制成功");
            Modal.clear();
            handleEncryptedFormData(formValues);
        } else {
            Toast.show({
                icon: "fail",
                content: "复制失败",
            });
        }

        // try {

        //     await navigator.clipboard.writeText(createPassword);
        //     Toast.show("复制成功");
        //     Modal.clear();
        //     handleEncryptedFormData(formValues);
        // } catch (err) {
        //     Toast.show("复制失败");
        // }

        // const pKRet = await request({
        //     method: "get",
        //     url: "/sys-config/getPublicKey",
        //     data: {},
        // });
        // const pK = pKRet?.data?.data || "";
        // saveRegister.run({
        //     farmerName: formValues.farmerName,
        //     phoneNumber: await rsaEncrypt(formValues.phoneNumber, pK),
        //     password: await rsaEncrypt(await randomPassword(8), pK),
        // });
    };

    const passwordModal = () => {
        return Modal.show({
            title: (
                <span style={{ fontSize: 14 }}>
                    用户创建成功，请妥善保存账号和密码！
                </span>
            ),
            showCloseButton: false,
            closeOnMaskClick: false,
            bodyClassName: "custom-modal-body",
            onClose: () => handleCancelCop,
            getContainer: () => document.getElementById("root"),
            content: (
                <div className="content">
                    {/* <div className="title">
                        用户创建成功，请妥善保存账号和密码！
                    </div> */}
                    <div>
                        <div>用户名：{formValues?.farmerName}</div>
                        <div>联系方式：{formValues?.phoneNumber}</div>
                        <div>密码：. . . . . . . . . .</div>
                    </div>
                    <div className="action">
                        <span className="cancel-btn" onClick={handleCancelCop}>
                            取消
                        </span>
                        <span className="ok-btnC" onClick={handleCopyAndClose}>
                            复制密码
                        </span>
                    </div>
                </div>
            ),
        });
    };
    // 保证formValues更新。
    useEffect(() => {
        const fetchData = async () => {
            try {
                const pKRet = await request({
                    method: "get",
                    url: "/sys-config/getPublicKey",
                    data: {},
                });
                const pK = pKRet?.data?.data || "";

                if (
                    formValues.farmerName &&
                    formValues.phoneNumber &&
                    canSubmit
                ) {
                    const encryptedPhoneNumber = await rsaEncrypt(
                        formValues.phoneNumber,
                        pK,
                    );
                    handleCheckRegister.run({
                        farmerName: formValues.farmerName,
                        phoneNumber: encryptedPhoneNumber,
                    });
                }
            } catch (error) {
                console.error("Error fetching data or encrypting:", error);
                // 可以添加更多的错误处理逻辑，如显示错误信息给用户
            }
        };

        fetchData();
    }, [canSubmit]);
    // optionslandType;
    const ok = (values: any) => {
        // 判断注册农户还没有有后台接口（09.27）

        setCanSubmit(true);
        setFormValues((preData) => ({ ...preData, ...values })); // 设置表单值以触发查询

        // 不判断,原有逻辑
        // handleEncryptedFormData(values)
    };

    // 加密表单数据
    const handleEncryptedFormData = async (values: any) => {
        try {
            const pKRet = await request({
                method: "get",
                url: "/sys-config/getPublicKey",
                data: {},
            });
            const pK = pKRet?.data?.data || "";
            const requestData = {
                // farmerName:
                //       values.processImg &&
                //       values.processImg.map((item: any) => item.value),
                //   processInstructions: values.processInstructions,
                farmerName: values.farmerName,
                landName: values.landName[0],
                // phoneNumber: await rsaEncrypt(values.phoneNumber, pK),
                phoneNumber: values.phoneNumber,
                plantBatch: values.plantBatch[0],
                landType: values.landType[0],
                purchaseTimes: values.purchaseTimes,
                purchaseBatch: values.purchaseBatch,
                purchaseWeight: values.purchaseWeight,
                purchaseUnitPrice: values.purchaseUnitPrice,
                purchaseTime: values.DatePicker,
                bagCount: values.bagCount,
                // processVideo: values?.processVideo?.[0]?.value,
            } as const;
            const signedData = await requestDataSign(
                requestData,
                "addMaterialPurchaseVo",
            );
            toastLoadingHandlerRef.current = showToastLoading({
                duration: 0,
                content: "正在上传",
            });
            productiveProcessEntryRequest.run(signedData);
        } catch (err: any) {
            console.log(err, "err");
            Toast.show({
                icon: "fail",
                content: err?.toString?.() || "加密失败",
            });
        }
    };

    const cancel = () => {
        console.log(111);
        myFormRef.resetFields();
        navigate("/b/home/<USER>/log");
    };

    const handleInput1Blur = async () => {
        const pKRet = await request({
            method: "get",
            url: "/sys-config/getPublicKey",
            data: {},
        });
        const pK = pKRet?.data?.data || "";
        // setInput1Focused(false);
        if (
            myFormRef.getFieldsValue().phoneNumber &&
            myFormRef.getFieldsValue().farmerName
        ) {
            purchaseCont.run({
                farmerName: myFormRef.getFieldsValue().farmerName,
                phoneNumber: await rsaEncrypt(
                    myFormRef.getFieldsValue().phoneNumber,
                    pK,
                ),
            });
            // myFormRef.setFieldsValue({
            //   purchaseTimes: purchaseCont.data?.data?.data,
            // });
        }
    };

    const handleInput2Blur = async () => {
        const pKRet = await request({
            method: "get",
            url: "/sys-config/getPublicKey",
            data: {},
        });
        const pK = pKRet?.data?.data || "";
        console.log("pKpKpKpKpKpKpK", pK);

        // setInput1Focused(false);
        if (
            myFormRef.getFieldsValue().phoneNumber &&
            myFormRef.getFieldsValue().farmerName
        ) {
            purchaseCont.run({
                farmerName: myFormRef.getFieldsValue().farmerName,
                phoneNumber: await rsaEncrypt(
                    myFormRef.getFieldsValue().phoneNumber,
                    pK,
                ),
            });
            // console.log('purchaseContpurchaseContpurchaseContpurchaseCont', purchaseCont.data);
            // myFormRef.setFieldsValue({
            //   purchaseTimes: purchaseCont.data?.data?.data,
            // });
        }
    };
    const selectValue = (e) => {
        // console.log(myFormRef.getFieldsValue().farmerInfo, "111111111");
        console.log(e, "eeeeeeeeeeeeee");
    };

    const pickerReflandName = useRef(null); // 创建一个ref来引用Picker实例
    const pickerReflandType = useRef(null); // 创建一个ref来引用Picker实例
    const pickerRefplantBatch = useRef(null); // 创建一个ref来引用Picker实例

    const handlePickerlandName = (selectedValues: any) => {
        console.log(selectedValues);
        // 更新Form的字段值
        myFormRef.setFieldsValue({ landName: selectedValues });
        plantNameByLandId.run({ landId: selectedValues[0] });
        setIsPickerDisabledlandType(selectedValues);

        myFormRef.setFieldsValue({ landType: undefined });
        myFormRef.setFieldsValue({ plantBatch: undefined });
        setLandTypeValue(undefined);
        setLandplantBatchValue(undefined);
        setIsPickerDisabledplantBatch(null);
    };

    const handlePickerConfirm = (selectedValues: any) => {
        // 更新Form的字段值
        // myFormRef.setFieldsValue({ landType: selectedValues });
        myFormRef.setFieldsValue({ landType: selectedValues });
        LandIdAndPlantName.run({
            landId: myFormRef.getFieldsValue().landName[0],
            plantName: selectedValues[0],
        });
        setIsPickerDisabledplantBatch(selectedValues);
        myFormRef.setFieldsValue({ plantBatch: undefined });

        setLandTypeValue(selectedValues);
    };

    useEffect(() => {
        const generateUniqueString = () => {
            const date = new Date();
            const dateString = date
                .toISOString()
                .slice(0, 10)
                .replace(/-/g, ""); // 获取当前日期并转换成YYYYMMDD格式
            const randomNumber = Math.floor(1000 + Math.random() * 9000); // 生成一个介于1000到9999之间的随机数
            return `${dateString}${randomNumber}`;
        };
        myFormRef.setFieldsValue({
            purchaseBatch: generateUniqueString(),
            // grower: selectedLand[0].farmerName
        });
    }, []);

    const handlePickerplantBatch = (selectedValues: any, data: any) => {
        const selectedItem = data.items.find(
            (item) => item.value === selectedValues[0],
        );
        if (!selectedItem.disabled) {
            // 更新Form的字段值
            myFormRef.setFieldsValue({ plantBatch: selectedValues });
            setLandplantBatchValue(selectedValues);
        } else {
            // 如果选择了被禁用的项，可以选择不执行任何操作，或者给出提示
            Toast.show("当前种植批次已被禁用！");
        }

        // LandIdAndPlantName.run({
        //     landId: myFormRef.getFieldsValue().landName[0],
        //     plantName: selectedValues[0],
        // });
    };
    //
    // 扫码
    const [codeResultShow, setCodeResultShow] = useState(false);

    const handleBoxCode = (boxCodeStr, continueScan) => {
        try {
            console.log(boxCodeStr, "扫码原始数据");
            const info =
                typeof boxCodeStr === "string"
                    ? JSON.parse(boxCodeStr)
                    : boxCodeStr;

            myFormRef.setFieldsValue({ farmerName: info.userName });
            myFormRef.setFieldsValue({ phoneNumber: info.phoneNumber });
            setCodeResultShow(false);
            closeScanQrCode();
        } catch (error) {
            console.error("处理扫码数据出错:", error);
            Toast.show({
                icon: "fail",
                content: "扫码数据格式错误",
            });
            setCodeResultShow(false);
            closeScanQrCode();
        }
    };

    const CodeResult = () => {
        console.log("CodeResult 被调用");
        setCodeResultShow(true);
        // 延迟启动扫描
        setTimeout(() => {
            try {
                console.log("开始启动扫码");
                startScanQrCode((boxCodeStr, continueScan) => {
                    handleBoxCode(boxCodeStr, continueScan);
                });
            } catch (error) {
                console.error("启动扫码出错:", error);
                setCodeResultShow(false);
            }
        }, 300);
    };

    // 确保组件卸载时关闭扫描器
    useEffect(() => {
        return () => {
            if (codeResultShow) {
                closeScanQrCode();
            }
        };
    }, [codeResultShow]);

    const validateNoSpacesName = (rule: any, value: any) => {
        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,30}$/);
        const verify = regExp.test(value);

        if (!value) {
            return Promise.reject("请输入农户姓名");
        } else if (value[0] == " " || value[value.length - 1] == " ") {
            return Promise.reject("字段前后不能输入空格！");
        } else if (verify === false) {
            if (value.length > 30) {
                return Promise.reject("请保持字符在30字符以内");
            } else {
                return Promise.reject("请输入农户姓名，支持中文、字母或数字!");
            }
        } else {
            return Promise.resolve();
        }
    };
    const validateNoSpacesBatch = (rule: any, value: any) => {
        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,50}$/);
        const verify = regExp.test(value);

        if (!value) {
            return Promise.reject("请输入收购批次");
        } else if (value[0] == " " || value[value.length - 1] == " ") {
            return Promise.reject("字段前后不能输入空格！");
        } else if (verify === false) {
            if (value.length > 30) {
                return Promise.reject("请保持字符在50字符以内");
            } else {
                return Promise.reject("请输入收购批次，支持中文、字母或数字!");
            }
        } else {
            return Promise.resolve();
        }
    };
    return (
        <div className={`${styles.logList}`}>
            <div className={`${styles.result}`}>
                <div style={{ fontSize: "16.8px" }}>
                    {/* {" "}
          <span
            style={{
              color: "red",
              fontFamily: "SimSun, sans-serif",
            }}
          >
            *
          </span>{" "} */}
                    &nbsp;&nbsp;农户信息
                </div>
                <div>
                    <div onClick={CodeResult}>
                        <ScanningOutline /> <a>扫码录入</a>
                    </div>
                    {codeResultShow && (
                        <div className={`${styles.resultView}`}>
                            {qrCodeScanElement}
                        </div>
                    )}
                </div>
            </div>
            <Form
                layout="horizontal"
                onFinish={ok}
                form={myFormRef}
                onValuesChange={formValChange}
            >
                {/* <Form.Item
                    name="farmerInfo"
                    label="农户信息"
                    rules={[{ required: true, message: "农户信息" }]}
                >

                    {
                        codeResultShow ? (
                            <div className={`${styles.result}`}>
                                <ScanQRCode setCodeResult={setCodeResult} />
                            </div>
                        ) : (
                            <div onClick={CodeResult}>
                                <ScanningOutline /> <a>扫码录入</a>
                            </div>
                        )
                    }
                </Form.Item> */}

                <Form.Item
                    name="farmerName"
                    label="农户姓名"
                    rules={[
                        { required: true, message: "" },
                        { validator: validateNoSpacesName },

                        // {
                        //     max: 50,
                        //     message: "不能超过50位",
                        // },
                    ]}
                >
                    <Input
                        style={{ "--text-align": "right" }}
                        // onChange={console.log}
                        placeholder="请填写"
                        onBlur={handleInput1Blur}
                    />
                </Form.Item>
                <Form.Item
                    name="phoneNumber"
                    label="联系方式"
                    rules={[
                        {
                            required: true,
                            message: "联系方式不能为空",
                        },
                        {
                            pattern: /^[1][3,4,5,6.7,8,9][0-9]{9}$/,
                            message: "请输入正确的联系方式",
                        },
                    ]}
                >
                    <Input
                        style={{ "--text-align": "right" }}
                        // onChange={handleInput2Change}
                        placeholder="请填写"
                        onBlur={handleInput2Blur}
                    />
                </Form.Item>
                <Form.Item
                    name="purchaseTimes"
                    label="被收购次数"
                    // rules={[{ required: true, message: "请输入被收购次数" }]}
                    disabled={true}
                >
                    <Input
                        style={{ "--text-align": "right" }}
                        onChange={console.log}
                        placeholder=""
                    />
                </Form.Item>
                <Form.Item name="name" label="收购信息"></Form.Item>
                <Form.Item
                    name="landName"
                    label="地块名称"
                    rules={[{ required: true, message: "请选择地块名称" }]}
                    // disabled={true}
                >
                    <Picker
                        ref={pickerReflandName}
                        columns={[options]}
                        onConfirm={handlePickerlandName}
                        getContainer={() => document.getElementById("root")}
                        // defaultValue={["Mon"]}
                    >
                        {(items, { open }) => (
                            <span
                                onClick={open}
                                className={`${styles.spanright}`}
                            >
                                {items[0]?.label ? (
                                    <span
                                        style={{
                                            color: "#333" /* 其他样式属性 */,
                                        }}
                                    >
                                        {items[0].label}
                                    </span>
                                ) : (
                                    "请选择"
                                )}
                            </span>
                        )}
                    </Picker>
                </Form.Item>
                <Form.Item
                    name="landType"
                    label="农作物类型"
                    rules={[{ required: true, message: "请选择农作物类型" }]}
                    // style={{textAlign:'right'}}
                >
                    <Picker
                        ref={pickerReflandType}
                        columns={[optionslandType]}
                        onConfirm={handlePickerConfirm}
                        value={landTypeValue}
                        getContainer={() => document.getElementById("root")}
                        // defaultValue={["Mon"]}
                    >
                        {(items, { open }) =>
                            isPickerDisabledlandType ? (
                                <span
                                    onClick={open}
                                    className={`${styles.spanright}`}
                                >
                                    {/* {landTypeValue ? items[0]?.label : "请选择"} */}
                                    {landTypeValue ? (
                                        <span style={{ color: "#333" }}>
                                            {items[0]?.label}
                                        </span> ? (
                                            <span style={{ color: "#333" }}>
                                                {items[0]?.label}
                                            </span>
                                        ) : (
                                            "请选择"
                                        )
                                    ) : (
                                        "请选择"
                                    )}
                                </span>
                            ) : (
                                <span
                                    style={{ color: "#909090" }}
                                    className={`${styles.spanright}`}
                                >
                                    {items[0]?.label || "请选择"}
                                </span>
                            )
                        }
                    </Picker>
                </Form.Item>
                <Form.Item
                    name="plantBatch"
                    label="种植批次"
                    rules={[{ required: true, message: "请选择种植批次" }]}
                >
                    <Picker
                        popupClassName="custom-popup-wrap"
                        ref={pickerRefplantBatch}
                        columns={[optionsplantBatch]}
                        onConfirm={handlePickerplantBatch}
                        value={landplantBatchValue}
                        renderLabel={renderItem}
                        getContainer={() => document.getElementById("root")}
                    >
                        {(items, { open }) =>
                            isPickerDisabledplantBatch ? (
                                <span
                                    onClick={open}
                                    className={`${styles.spanright}`}
                                >
                                    {landplantBatchValue ? (
                                        <span style={{ color: "#333" }}>
                                            {items[0]?.label}
                                        </span> ? (
                                            <span style={{ color: "#333" }}>
                                                {items[0]?.label}
                                            </span>
                                        ) : (
                                            "请选择"
                                        )
                                    ) : (
                                        "请选择"
                                    )}
                                </span>
                            ) : (
                                <span
                                    style={{ color: "#909090" }}
                                    className={`${styles.spanright}`}
                                >
                                    {"请选择"}
                                </span>
                            )
                        }
                    </Picker>
                    {/* <Input onChange={console.log} placeholder="请输入姓名" /> */}
                </Form.Item>
                <Form.Item
                    name="purchaseBatch"
                    label="收购批次"
                    rules={[
                        { required: true, message: "" },
                        { validator: validateNoSpacesBatch },
                    ]}
                >
                    <Input
                        style={{ "--text-align": "right" }}
                        onChange={console.log}
                        placeholder="请填写"
                    />
                </Form.Item>
                <Form.Item
                    name="DatePicker"
                    label="收购时间"
                    trigger="onConfirm"
                    rules={[{ required: true, message: "请选择收购时间" }]}
                    onClick={(e, datePickerRef: RefObject<DatePickerRef>) => {
                        datePickerRef.current?.open();
                    }}
                >
                    <DatePicker
                        precision="second"
                        getContainer={() => document.getElementById("root")}
                    >
                        {(value) =>
                            value ? (
                                dayjs(value).format("YYYY-MM-DD HH:mm:ss")
                            ) : (
                                <span
                                    className={`${styles.datePickerPlaceholder}`}
                                >
                                    请选择收购时间
                                </span>
                            )
                        }
                    </DatePicker>
                </Form.Item>
                <Form.Item
                    name="purchaseWeight"
                    label="收购重量"
                    extra={<div className={`${styles.customExtra}`}>吨</div>}
                    rules={[
                        {
                            required: true,
                            message: "请输入收购重量",
                            min: 0.01,
                            type: "number",
                        },
                    ]}
                >
                    <Stepper
                        style={{ "--input-width": "68px" }}
                        digits={2}
                        min={0}
                        max={100000}
                        step={0.01}
                        precision={2}
                    />
                </Form.Item>

                <Form.Item
                    name="bagCount"
                    label="装袋数量"
                    extra={<div className={`${styles.customExtra}`}>袋</div>}
                    rules={[
                        {
                            required: true,
                            message: "请输入装袋数量",
                            min: 0.1,
                            type: "number",
                        },
                    ]}
                >
                    <Stepper
                        style={{ "--input-width": "68px" }}
                        digits={1}
                        min={0}
                        max={10000000}
                        step={0.1}
                        precision={1}
                    />
                </Form.Item>

                <Form.Item
                    name="purchaseUnitPrice"
                    label="收购单价"
                    extra={<div className={`${styles.customExtra}`}>元/吨</div>}
                    rules={[
                        {
                            required: true,
                            message: "请输入收购单价",
                            min: 0.01,
                            type: "number",
                        },
                    ]}
                >
                    <Stepper
                        style={{ "--input-width": "68px" }}
                        digits={2}
                        min={0}
                        max={100000}
                        step={0.01}
                        precision={2}
                    />
                </Form.Item>
                <div className={`${styles.btnbox}`}>
                    <Button
                        color="primary"
                        fill="none"
                        className={`${styles.Btn}`}
                        onClick={cancel}
                    >
                        取消
                    </Button>
                    <Button
                        color="primary"
                        fill="none"
                        type="submit"
                        className={`${styles.Btn}`}
                    >
                        确认
                    </Button>
                </div>
            </Form>
        </div>
    );
};

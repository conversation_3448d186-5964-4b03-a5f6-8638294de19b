import { useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import {
    Button,
    Input,
    TextArea,
    Picker,
    Space,
    DotLoading,
    Ellipsis,
    Toast,
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
    UpOutline,
    DownOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import { useRequest, useSetState } from "ahooks";
import { showConfirmModal } from "@/utils";
import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";
import CollapseTable from "@/components/collapse-table";

import iconScanQrCode from "@/assets/imgs/scan-qrcode.png";

import { bSideRequests } from "@/services";
import { REPOSITORY_TYPE_CONSTANTS } from "@/config";
import useStore, { useTheme } from "@/store";
import styles from "./index.module.less";

export default () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const [searchParams] = useSearchParams();
    const userInfo = useStore<any>((state) => state.userInfo);
    // 获取列表类型，判断是否来自已作废列表
    const listType = searchParams.get("listType");
    const isFromDeprecatedList = listType === "deprecated";

    const getInboundDetailRequest = useRequest(
        () => {
            if (!id) {
                throw new Error("缺少id");
            }
            return bSideRequests.getWarehouseDetail(id);
        },
        {
            refreshDeps: [id],
        },
    );

    const deprecatePackRequest = useRequest(
        () => {
            if (!id) {
                throw new Error("缺少id");
            }
            return bSideRequests.cancelWare(id);
        },
        {
            manual: true,
            onSuccess() {
                Toast.show({
                    icon: "success",
                    content: "作废成功",
                });
                // 根据来源决定跳转路径
                if (isFromDeprecatedList) {
                    navigate(
                        "/b/home/<USER>/log?listType=deprecated",
                    );
                } else {
                    navigate("/b/home/<USER>/log");
                }
            },
        },
    );

    const inboundDetail = getInboundDetailRequest.data?.data?.data || {};

    if (getInboundDetailRequest.loading) {
        return (
            <div
                style={{
                    paddingTop: "50px",
                    textAlign: "center",
                    color: "var(--adm-color-weak)",
                }}
            >
                <div style={{ fontSize: 24, marginBottom: 24 }}>
                    <DotLoading />
                </div>
                正在加载数据
            </div>
        );
    }
    return (
        <div className={`${styles.inboundLogDetail}`}>
            <List>
                <List.Item
                    extra={
                        <Ellipsis
                            content={inboundDetail?.inboundNo || "-"}
                        ></Ellipsis>
                    }
                >
                    入库单号
                </List.Item>
                <List.Item extra={inboundDetail?.warehouseName || "-"}>
                    仓库名称
                </List.Item>
                <List.Item extra={inboundDetail?.orgName || "-"}>
                    生产加工企业
                </List.Item>
                <List.Item extra={inboundDetail?.productName || "-"}>
                    产品名称
                </List.Item>
                <List.Item extra={inboundDetail?.productionBatch || "-"}>
                    生产批次
                </List.Item>
                <List.Item extra={inboundDetail?.inboundNum || "-"}>
                    入库数量
                </List.Item>

                <List.Item
                    extra={
                        inboundDetail?.inboundTime
                            ? dayjs(inboundDetail.inboundTime).format(
                                  "YYYY-MM-DD HH:mm:ss",
                              )
                            : "-"
                    }
                >
                    入库时间
                </List.Item>
                {/* <List.Item
                    extra={
                        ![undefined, null].includes(
                            inboundDetail?.storehouseType,
                        )
                            ? // @ts-ignore
                              REPOSITORY_TYPE_CONSTANTS
                                  .REPOSITORY_TYPE_MAP_BY_VALUE[
                                  inboundDetail.storehouseType
                              ].name
                            : "-"
                    }
                >
                    仓库类型
                </List.Item> */}
            </List>
            {!isFromDeprecatedList && userInfo?.identity == 8 && (
                <Button
                    loading={deprecatePackRequest.loading}
                    style={{ marginTop: 50 }}
                    className={`${styles.Btn}`}
                    fill="none"
                    color="primary"
                    shape="rounded"
                    block
                    type="submit"
                    onClick={() => {
                        showConfirmModal({
                            title: "确认作废",
                            content: "请确认是否作废入库信息？",
                            confirmBtnProps: {
                                color: "danger",
                            },
                            onConfirm() {
                                deprecatePackRequest.run();
                            },
                        });
                    }}
                >
                    作废
                </Button>
            )}
        </div>
    );
};

import React, { useState, useCallback, useEffect, useRef } from "react";
import {
  useNavigate,
  unstable_useBlocker as useBlocker,
} from "react-router-dom";
import {
  Button,
  Input,
  Picker,
  Space,
  Modal,
  ImageUploader,
  Toast,
} from "antd-mobile";
import {
  UnorderedListOutline,
  PayCircleOutline,
  SetOutline,
  UpOutline,
  DownOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import { useRequest } from "ahooks";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";
import CollapseTable from "@/components/collapse-table";
import Upload from "@/components/upload";
import TextArea from "@/components/text-area";

import iconScanQrCode from "@/assets/imgs/scan-qrcode.png";

import { useConfirmBlock } from "@/hooks";
import {
  showToastLoading,
  showConfirmModal,
  signatureByEncryptedPrivateKey,
  requestDataSign,
} from "@/utils";
import { bSideRequests } from "@/services";
import { } from "@/config";

import styles from "./index.module.less";

import type {
  unstable_Blocker as Blocker,
  unstable_BlockerFunction as BlockerFunction,
} from "react-router-dom";
import type { ToastLoadingHandler } from "@/utils";

const Entry = () => {
  const navigate = useNavigate();
  const [entryForm] = Form.useForm();

  const [isFormFinished, setIsFormFinished] = useState(true);
  const shouldBlock = useCallback<BlockerFunction>(
    ({ currentLocation, nextLocation }) => {
      return (
        !isFormFinished &&
        currentLocation.pathname !== nextLocation.pathname
      );
    },
    [isFormFinished],
  );
  useConfirmBlock({
    content: "离开后编辑内容不保存，确认离开吗？",
    shouldBlock: shouldBlock,
  });

  const toastLoadingHandlerRef = useRef<ToastLoadingHandler | null>(null);
  const productiveProcessEntryRequest = useRequest(
    bSideRequests.productiveProcessEntry,
    {
      manual: true,
      onSuccess() {
        toastLoadingHandlerRef?.current?.close?.();
        setIsFormFinished(true);
        Toast.show("上报成功");
        entryForm.resetFields();
      },
    },
  );

  const handleReport = async (values: any) => {
    console.log(values, "values");

    try {
      const requestData = {
        processImg:
          values.processImg &&
          values.processImg.map((item: any) => item.value),
        processInstructions: values.processInstructions,
        processName: values.processName,
        processVideo: values?.processVideo?.[0]?.value,
      } as const;
      const signedData = await requestDataSign(
        requestData,
        "addProcessVo",
      );
      toastLoadingHandlerRef.current = showToastLoading({
        duration: 0,
        content: "正在上传",
      });
      productiveProcessEntryRequest.run(signedData);
    } catch (err: any) {
      console.log(err, "err");
      Toast.show({
        icon: "fail",
        content: err?.toString?.() || "加密失败",
      });
    }
  };

  const productVideoAcceptTypes = ['.mp4', '.webm', '.mov', '.m4v'];

  return (
    <div className={`${styles.scanInbound}`}>
      <Form
        form={entryForm}
        style={{
          "--border-bottom": "none",
        }}
        onValuesChange={() => {
          setIsFormFinished(false);
        }}
        onFinishFailed={(failInfo) => {
          const errorMsg = failInfo?.errorFields?.[0]?.errors?.[0];
          Toast.show(errorMsg);
        }}
        onFinish={(values) => {
          showConfirmModal({
            title: "确认上传",
            onConfirm() {
              handleReport(values);
            },
          });
        }}
      >
        <Form.Item
          layout="horizontal"
          label="名称"
          name="processName"
          rules={[
            {
              required: true,
              message: "请输入名称",
            },
            {
              max: 10,
              message: "不能超过10位",
            },
            {
              validator: (_: any, value: string) => {
                if (value && value.includes(' ')) {
                  return Promise.reject(new Error('名称不允许包含空格'));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          style={{
            // @ts-ignore
            "--form-item-label-font-size": "var(--text-sm)",
            "--form-item-label-color": "#666",
          }}
          label="过程信息采集"
          name="processImg"
        >
          <Upload
            columns={3}
            maxCount={3}
            accept={".jpg,.jpeg,.png"}
            beforeUpload={(file) => {
              if (file.size > 5 * 1024 * 1024) {
                Toast.show(`大小不超过5MB`);
                return null;
              }
              if (
                !["image/jpeg", "image/png"].includes(file.type)
              ) {
                Toast.show("请上传jpg/jpeg/png格式图片");
                return null;
              }
              return file;
            }}
            help={
              <div>
                <div>最多支持上传3张图片，</div>
                <div>大小不超过5MB</div>
              </div>
            }
            onUploadQueueChange={(tasks) => { }}
          ></Upload>
        </Form.Item>
        <Form.Item
          style={{
            // @ts-ignore
            "--border-inner": "none",
          }}
          name="processVideo"
        >
          <Upload
            columns={3}
            maxCount={1}
            beforeUpload={(file) => {
              if (file.size > 50 * 1024 * 1024) {
                Toast.show("大小不超过50MB");
                return null;
              }
              console.log(file.name, 'fnnnn')
              const fileNameSplit = file.name.split('.')
              const fileType = fileNameSplit[fileNameSplit.length - 1]?.toLowerCase();
              if (!productVideoAcceptTypes.includes('.' + fileType)) {
                Toast.show("请上传mp4/webm/mov/m4v格式视频");
                return null;
              }
              return file;
            }}
            type="video"
            accept={productVideoAcceptTypes.join(',')}
            help="大小不超过50MB"
          ></Upload>
        </Form.Item>
        <Form.Item
          label="生产过程说明"
          style={{
            // @ts-ignore
            "--border-inner": "none",
            "--form-item-label-font-size": "var(--text-sm)",
            "--form-item-label-color": "#666",
          }}
          name="processInstructions"
        >
          <TextArea rows={4} showCount maxLength={300}></TextArea>
        </Form.Item>
        {/* <Button
                 className="loginBtn"
                    style={{ marginTop: 30 }}
                    shape="rounded"
                    block
                    color="primary"
                    type="submit"
                >
                    上报
                </Button> */}
        <Button
          className={`${styles.Btn}`}
          block
          style={{ marginTop: 30 }}
          fill='none'
          type="submit"
          color="primary"
        >
          上报
        </Button>
      </Form>
    </div>
  );
};

export default Entry;

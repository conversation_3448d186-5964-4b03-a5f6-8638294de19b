import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Button, Input, <PERSON><PERSON><PERSON>, Picker, DotLoading } from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
    StarFill,
} from "antd-mobile-icons";
import { useRequest, useSetState } from "ahooks";
import dayjs from "dayjs";
import classNames from "classnames";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";

import { cSideRequests } from "@/services";

import styles from "./index.module.less";

import badgeIcon from "@/assets/imgs/c/certificate/badge.png";
import footerImg from "@/assets/imgs/c/certificate/footer.png";
import chapterIcon from "@/assets/imgs/c/certificate/chapter.png";

export default () => {
    const { transactionId } = useParams();

    const getTransactionChainInfoRequest = useRequest(() => {
        if (!transactionId) {
            return Promise.reject();
        }
        return cSideRequests.getTransactionChainInfo(transactionId);
    }, {});
    const transactionChainInfo =
        getTransactionChainInfoRequest.data?.data?.data || {};

    return (
        <div className={`_global_page ${styles.cCertificate}`}>
            <NavBar>中移链查证结果</NavBar>
            {getTransactionChainInfoRequest.loading && (
                <div
                    style={{
                        paddingTop: "200px",
                        textAlign: "center",
                        color: "var(--adm-color-weak)",
                    }}
                >
                    <div style={{ fontSize: 24, marginBottom: 24 }}>
                        <DotLoading />
                    </div>
                    正在加载数据
                </div>
            )}
            <div
                style={{
                    display: getTransactionChainInfoRequest.loading
                        ? "none"
                        : "block",
                }}
                className="main"
            >
                <div className="certificate">
                    <div className="certificate__badge">
                        <img src={badgeIcon} />
                    </div>
                    <div className="certificate__title">中移链溯源证书</div>
                    <div className="certificate__content">
                        <div className="certificate__content-item">
                            <div className="certificate__content-item-label">
                                所在链名称：
                            </div>
                            <div className="certificate__content-item-value">
                                中移链
                            </div>
                        </div>
                        <div className="certificate__content-item">
                            <div className="certificate__content-item-label">
                                所在区块：
                            </div>
                            <div className="certificate__content-item-value">
                                {transactionChainInfo.blockNum ?? "-"}
                            </div>
                        </div>
                        <div className="certificate__content-item certificate__content-custom">
                            <div className="certificate__content-item-label">
                                溯源唯一标识：
                            </div>
                            <div className="certificate__content-item-value">
                                {transactionChainInfo.transactionId ? (
                                    <>
                                        {/* <div>
                                            {transactionChainInfo.transactionId.slice(
                                                0,
                                                transactionChainInfo
                                                    .transactionId.length / 2,
                                            )}
                                        </div>
                                        <div>
                                            {transactionChainInfo.transactionId.slice(
                                                transactionChainInfo
                                                    .transactionId.length / 2,
                                            )}
                                        </div> */}
                                        {transactionChainInfo.transactionId}
                                    </>
                                ) : (
                                    "-"
                                )}
                            </div>
                        </div>
                        <div className="certificate__content-item">
                            <div className="certificate__content-item-label">
                                信息上传时间：
                            </div>
                            <div className="certificate__content-item-value">
                                {transactionChainInfo.transactionTime
                                    ? dayjs(
                                          transactionChainInfo.transactionTime,
                                      ).format("YYYY-MM-DD HH:mm:ss")
                                    : "-"}
                            </div>
                        </div>
                        <div className="certificate__content-item">
                            <div className="certificate__content-item-label">
                                认证时间：
                            </div>
                            <div className="certificate__content-item-value">
                                {transactionChainInfo.blockTime
                                    ? dayjs(
                                          transactionChainInfo.blockTime,
                                      ).format("YYYY-MM-DD HH:mm:ss")
                                    : "-"}
                            </div>
                        </div>
                        <div className="text">
                            *此证书仅作为上链数据真实性和完整性的参考依据，不涉及商品品质的任何承诺。
                        </div>
                        <div className="chapter_Icon">
                            <img src={chapterIcon} />
                        </div>
                    </div>
                    <div className="certificate__footer">
                        <img src={footerImg} />
                    </div>
                </div>
            </div>
        </div>
    );
};

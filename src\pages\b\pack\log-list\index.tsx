import { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
    Button,
    Input,
    <PERSON><PERSON><PERSON>,
    Picker,
    InfiniteScroll,
    Toast,
    <PERSON>rror<PERSON><PERSON>,
    DotLoading,
    PullToRefresh,
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import { useRequest, useInfiniteScroll, useDebounce } from "ahooks";
import useUrlState from "@ahooksjs/use-url-state";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";

import { INFINITE_LIST_PAGE_SIZE } from "@/config";
import { bSideRequests } from "@/services";

import styles from "./index.module.less";

export default () => {
    const navigate = useNavigate();

    const [urlState, setUrlState] = useUrlState<{
        listType: "normal" | "deprecated";
    }>(
        {
            listType: "normal",
        },
        {
            navigateMode: "replace",
        },
    );

    const getListRequest = useInfiniteScroll(
        (d) => {
            const page = d
                ? Math.ceil(d.list.length / INFINITE_LIST_PAGE_SIZE) + 1
                : 1;
            const stateMap = {
                normal: 1,
                deprecated: 2,
            } as any;
            return bSideRequests
                .getPackCodeList({
                    pageIndex: page,
                    pageSize: INFINITE_LIST_PAGE_SIZE,
                    state: stateMap[urlState.listType],
                })
                .then((res) => {
                    const listInfo = res?.data?.data || {};
                    return {
                        list: listInfo?.records || [],
                        total: listInfo?.total || 0,
                    };
                });
        },
        {
            reloadDeps: [urlState.listType],
        },
    );
    const hasMore =
        getListRequest.data &&
        getListRequest.data.list.length < getListRequest.data.total;

    const data = getListRequest?.data?.list || [];

    const renderList = () => {
        if (getListRequest.loading) {
            return (
                <div
                    style={{
                        paddingTop: "30px",
                        textAlign: "center",
                        color: "var(--adm-color-weak)",
                    }}
                >
                    <div style={{ fontSize: 24, marginBottom: 24 }}>
                        <DotLoading />
                    </div>
                    正在加载数据
                </div>
            );
        }
        if (data.length === 0) {
            return (
                <ErrorBlock
                    image={<div></div>}
                    title="暂无数据"
                    description=""
                />
            );
        }
        return (
            <div>
                <PullToRefresh
                    onRefresh={async () => {
                        await getListRequest.reloadAsync();
                    }}
                >
                    <List className="packLogList">
                        {data.map((item) => {
                            return (
                                <List.Item
                                    key={item?.id}
                                    onClick={() => {
                                        navigate(item?.id?.toString());
                                    }}
                                >
                                    <div className="packLogList__item">
                                        <div>{item.boxCode || "-"}</div>
                                        <div className="packLogList__item--info">
                                            <div>{item.productName || "-"}</div>
                                            <div>
                                                {item.activateTime
                                                    ? dayjs(
                                                          item.activateTime,
                                                      ).format(
                                                          "YYYY-MM-DD HH:mm:ss",
                                                      )
                                                    : "-"}
                                            </div>
                                        </div>
                                    </div>
                                </List.Item>
                            );
                        })}
                    </List>
                </PullToRefresh>
                <InfiniteScroll
                    loadMore={async () => {
                        await getListRequest.loadMoreAsync();
                    }}
                    hasMore={hasMore || false}
                />
            </div>
        );
    };

    return (
        <div className={`${styles.logList}`}>
            <CapsuleTabs
                activeKey={urlState.listType}
                onChange={(key: any) => {
                    setUrlState({
                        listType: key,
                    });
                }}
            >
                <CapsuleTabs.Tab title="正常" key="normal"></CapsuleTabs.Tab>
                <CapsuleTabs.Tab
                    title="已作废"
                    key="deprecated"
                ></CapsuleTabs.Tab>
            </CapsuleTabs>
            {renderList()}
        </div>
    );
};

.inboundLogDetail {
    margin: 10px;
    padding: 20px 16px;
    padding-bottom: 58px;
    background: var(--page-block-color);
    border-radius: var(--border-radius);
    :global {
        .adm-list-body {
            border-bottom: none;
        }
        .inboundInfoForm__unloadLocation {
            .adm-list-item-content {
                padding-right: 0;
            }
            .adm-form-item-feedback-error {
                text-align: right;
            }
            input::placeholder {
                text-align: right;
            }
        }
        .boxCodeList {
            padding-top: 5px;
            border-bottom: solid 1px var(--adm-color-border);
            th {
                color: var(--text-main);
                font-size: var(--text-md);
                font-weight: normal;
            }
        }
        .inboundInfoForm__requiredLabel {
            position: relative;
            .inboundInfoForm__required {
                position: absolute;
                left: -0.6em;
                top: 0;
                font-family: SimSun, sans-serif;
                color: var(--adm-color-danger);
                -webkit-user-select: none;
                user-select: none;
            }
        }
    }
}

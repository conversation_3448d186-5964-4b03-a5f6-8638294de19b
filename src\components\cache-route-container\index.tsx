import React, { useEffect, useState, useRef, memo } from "react";
import {
    useOutlet,
    useLocation,
    resolvePath,
    useOutletContext,
    useMatches,
} from "react-router-dom";

/**
 * @expiremental
 */
const CacheRouteContainer = ({ shouldCache = () => true }: any) => {
    const outletContext = useOutletContext<any>();

    const cachedRoutesRef = useRef<any>({});
    const outlet = useOutlet(outletContext);
    const location = useLocation();
    const matches = useMatches();

    let cur = null;
    if (shouldCache(location, matches)) {
        if (!cachedRoutesRef.current?.[location.pathname]) {
            cachedRoutesRef.current[location.pathname] = outlet;
        }
        cur = null;
    } else {
        cur = outlet;
    }

    return [
        ...Object.keys(cachedRoutesRef.current).map((routeKey) => {
            return (
                <div
                    key={routeKey}
                    style={{
                        height: "100%",
                        width: "100%",
                        overflow: "auto",
                        display:
                            routeKey === location.pathname ? "block" : "none",
                    }}
                >
                    {cachedRoutesRef.current?.[routeKey]}
                </div>
            );
        }),
        cur,
    ];
};

CacheRouteContainer.useIsRouteShow = (cb: any) => {
    const location = useLocation();
    const initialLocationPath = useRef<string | null>(null);
    useEffect(() => {
        initialLocationPath.current = location.pathname;
        console.log(location, "location");
    }, []);
    useEffect(() => {
        if (location.pathname === initialLocationPath.current) {
            console.log("show");
            cb(true);
        } else {
            cb(false);
        }
    }, [location.pathname]);
};

export default CacheRouteContainer;

{
    "compilerOptions": {
        "target": "ES2020",
        "useDefineForClassFields": true,
        "lib": ["ES2020", "DOM", "DOM.Iterable"],
        "module": "ESNext",
        "skipLibCheck": true,

        /* Bundler mode */
        "moduleResolution": "node",
        // "allowImportingTsExtensions": true,
        "resolveJsonModule": true,
        "isolatedModules": true,
        "noEmit": true,
        "jsx": "react-jsx",

        /* Linting */
        "strict": true,
        // "noUnusedLocals": true,
        // "noUnusedParameters": true,
        "noFallthroughCasesInSwitch": true,
        "baseUrl": "./src", // 这是你的源码基路径
        "paths": {
            "@/pages/*": ["pages/*"] // 定义别名
        }
    },
    "include": ["src"],
    "references": [{ "path": "./tsconfig.node.json" }]
}

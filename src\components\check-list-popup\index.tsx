import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Ellipsis } from "antd-mobile";
import { LeftOutline, RightOutline } from "antd-mobile-icons";
import { useNavigate } from "react-router-dom";

import styles from "./index.module.less";

import type { CapsuleTabsProps } from "antd-mobile";

interface ICheckListPopup {
    title?: string;
    items: { label: any; value: any }[];
    maskClosable?: boolean;
    multiple?: boolean;
    value?: (string | number)[];
    onChange?: (value: ICheckListPopup["value"]) => void;
    placeholder?: string;
}

const CheckListPopup = (props: ICheckListPopup) => {
    const {
        value,
        onChange,
        items,
        title,
        maskClosable = false,
        multiple = false,
        placeholder,
    } = props;

    const [visible, setVisible] = useState(false);

    const [internalCheckedValue, setInternalCheckedValue] = useState<
        ICheckListPopup["value"]
    >([]);

    return (
        <div className={styles.checkListPopup}>
            <div
                onClick={() => {
                    setInternalCheckedValue(value || []);
                    setVisible(true);
                }}
                className={styles.checkListPopup__trigger}
            >
                <div>
                    {value && value?.length > 0 ? (
                        <Ellipsis
                            style={{ color: "var(--adm-color-text)" }}
                            content={value
                                .map((valueItem) => {
                                    return (items || []).find(
                                        (item) => item.value === valueItem,
                                    )?.label;
                                })
                                .filter(Boolean)
                                .join("，")}
                        ></Ellipsis>
                    ) : (
                        placeholder
                    )}
                </div>
                <RightOutline
                    style={{
                        color: "var((--text-secondary)",
                        position: "relative",
                        left: "4px",
                    }}
                ></RightOutline>
            </div>
            <Popup
                visible={visible}
                getContainer={() => {
                    return document.getElementById("root")!;
                }}
                onMaskClick={() => {
                    if (maskClosable) {
                        setVisible(false);
                    }
                }}
                destroyOnClose
            >
                <div className={styles.checkListPopup__header}>
                    <Button
                        color="primary"
                        fill="none"
                        onClick={() => {
                            setVisible(false);
                        }}
                    >
                        取消
                    </Button>
                    <div>{title}</div>
                    <Button
                        color="primary"
                        fill="none"
                        onClick={() => {
                            onChange?.(internalCheckedValue);
                            setVisible(false);
                        }}
                    >
                        确定
                    </Button>
                </div>
                <div className={styles.checkListPopup__checkListContainer}>
                    <CheckList
                        multiple={multiple}
                        value={internalCheckedValue}
                        onChange={(val) => {
                            setInternalCheckedValue(val);
                        }}
                    >
                        {items.map((item) => (
                            <CheckList.Item key={item.value} value={item.value}>
                                {item.label}
                            </CheckList.Item>
                        ))}
                    </CheckList>
                </div>
            </Popup>
        </div>
    );
};

export default CheckListPopup;

import request from "@/services";
import {
    TRANSPORTATION_STATE_CONSTANTS,
    TRANSPORTATION_TYPE_CONSTANTS,
} from "@/config";

export const getGraphicVerificationCode = () => {
    return request({
        url: "/user/getImageCode",
        method: "get",
        responseType: "blob",
        // @ts-ignore
        __disableGlobalToast: true,
    });
};
//应用标题信息信息
//标题所属页面(0:web端,1:h5端),不传则返回所有
export const getTitle = (data:{titleType?:number}) => {
    return request({
        url: '/getTitle',
        method: 'post',
        data,
    });
};


export const login = (obj: any) => {
    return request({
        url: "/login",
        method: "post",
        data: obj,
        headers: {
            ['x-request-id']: obj.xRequestId
        }
    });
};
export const logout = () => {
    return request({
        url: "/logout",
        method: "post",
        data: {},
    });
};

export const getUserInfo = () => {
    return request({
        url: "/user/info",
        method: "get",
    });
};

export const getUserMenuList = () => {
    return request({
        url: "/menu/menuList",
        method: "get",
    });
};

export const productiveProcessEntry = (data: any) => {
    return request({
        url: "/process/add",
        method: "post",
        data: data,
    });
};
// 初次修改密码
export const firstReset = (data:any) => {
  return request({
      method: 'post',
      url: '/user/firstReset',
      data
  });
};
// 修改手机号
export const modifyPhone = (data:any) => {
  return request({
      method: 'post',
      url: '/user/modifyPhone',
      data
  });
};
// 修改密码
export const modifyPassword = (data:any) => {
  return request({
      method: 'post',
      url: '/user/modifyPassword',
      data
  });
};
export const usercode = () => {
  return request({
      url: "/user/getH5UserQrCode ",
      method: "get",
      responseType: 'arraybuffer'  // 设置响应类型为二进制数据
  });
};
export interface GetProductiveProcessListData {
    endTime?: string;
    pageIndex: number;
    pageSize: number;
    processName?: string;
    startTime?: string;
    /** 状态， 0正常，1作废 */
    state?: 0 | 1;
}
export const getProductiveProcessList = (
    data: GetProductiveProcessListData,
) => {
    return request({
        url: "/process/pageByApp",
        method: "post",
        data: data,
    });
};

export const getProductiveProcessDetail = (processId: number | string) => {
    return request({
        url: "/process/getDetailByApp",
        method: "get",
        params: {
            processId,
        },
    });
};

export const deprecateProductiveProcess = (processId: number | string) => {
    return request({
        url: "/process/cancelByApp",
        method: "post",
        data: {
            id: processId,
        },
    });
};

export interface PackScanEntryData {
    boxCode: string;
    traceCode: (number | string)[];
}
export const packScanEntry = (data: PackScanEntryData) => {
    return request({
        url: "/box/pack",
        method: "post",
        data: data,
        // @ts-ignore
        __disableGlobalToast: true,
    });
};

export interface GetPackCodeListProps {
    pageIndex: number;
    pageSize: number;
    state?: number;
}
export const getPackCodeList = (data: GetPackCodeListProps) => {
    return request({
        url: "/box/boxPage",
        method: "post",
        data: data,
    });
};

export const getPackCodeDetail = (id: number | string) => {
    return request({
        url: "/box/detail",
        method: "get",
        params: {
            id,
        },
    });
};

export const deprecatePack = (id: number | string) => {
    return request({
        url: "/box/cancel",
        method: "post",
        data: {
            id: id,
        },
    });
};

export interface InboundScanEntryData {
    boxIds: (number | string)[];
    position: string;
    storehouse: string;
    storehouseArea: string;
    storehouseType: number;
    warehouseNumber: string;
}
export const inboundScanEntry = (data: InboundScanEntryData) => {
    return request({
        url: "/inWarehouse/add",
        method: "post",
        data: data,
        // @ts-ignore
        __disableGlobalToast: true,
    });
};

export interface GetInboundListProps {
    pageIndex: number;
    pageSize: number;
}
export const getInboundList = (props: GetInboundListProps) => {
    return request({
        url: "/inWarehouse/getWarehouseNumberList",
        method: "post",
        data: props,
    });
};

export const getInboundDetail = (id: number | string) => {
    return request({
        url: "/inWarehouse/getDetail",
        method: "get",
        params: {
            inWarehouseId: id,
        },
    });
};

export interface GetAreaListRetData {
    id: number;
    province: string;
    createTime?: any;
    modifyTime?: any;
    delTime?: any;
    delUser?: any;
    delFlag: number;
}

export interface GetAreaListRet {
    code: number;
    message: string;
    data: GetAreaListRetData[];
}
export const getAreaList = () => {
    return request<GetAreaListRet>({
        url: "/area/getList",
        method: "get",
    });
};

interface OutboundScanEntry {
    areaIds?: (number | string)[];
    boxIds: (number | string)[];
    dealer?: string;
    logisticsEnterprises?: string;
    logisticsNumber?: string;
    orderNumber?: string;
    outWarehouseNumber: string;
    salesChannels?: string;
}
export const outboundScanEntry = (data: OutboundScanEntry) => {
    return request({
        url: "/outWarehouse/add",
        method: "post",
        data: data,
        // @ts-ignore
        __disableGlobalToast: true,
    });
};

export interface GetOutboundListProps {
    pageIndex: number;
    pageSize: number;
}
export const getOutboundList = (props: GetOutboundListProps) => {
    return request({
        url: "/outWarehouse/getWarehouseNumberList",
        method: "post",
        data: props,
    });
};

export const getOutboundDetail = (id: number | string) => {
    return request({
        url: "/outWarehouse/getDetail",
        method: "get",
        params: {
            outWarehouseId: id,
        },
    });
};

interface TransportationScanEntryData {
    boxList: (string | number)[];
    loEnterprises?: string;
    loNumber?: string;
    loadingLocation?: string;
    transportationType?: number;
    /** 1:自行运输; 2:委托运输 */
    type: 1 | 2;
}
interface SignedTransportationScanEntryData {
    addLogisticsVo: TransportationScanEntryData;
    paramStr: string;
    signature: string;
}
export const transportationScanEntry = async (
    data: SignedTransportationScanEntryData,
) => {
    return request({
        url: "/logistics/add",
        method: "post",
        data: data,
        // @ts-ignore
        __disableGlobalToast: true,
    });
};
export interface GetTransportationListProps {
    beginTime?: string;
    endTime?: string;
    pageIndex: number;
    pageSize: number;
    state?: (typeof TRANSPORTATION_STATE_CONSTANTS.TRANSPORTATION_STATE_VALUES)[number];
    transNumber?: string;
    type?: number;
}
export const getTransportationList = (props: GetTransportationListProps) => {
    return request({
        url: "/logistics/getLogisticsList",
        method: "post",
        data: props,
    });
};

export interface GetTransportationDetailRetDataBoxTo {
    productName: string;
    boxCode: string;
}
export interface GetTransportationDetailRetData {
    boxTo: GetTransportationDetailRetDataBoxTo[];
    id: number;
    loEnterprises: string;
    loNumber: string;
    loadingLocation: string;
    transNumber: string;
    transactionId: string;
    transactionTime: string;
    unloadTransactionId: string;
    unloadTransactionTime: string;
    unloadingLocation: string;
    state: (typeof TRANSPORTATION_STATE_CONSTANTS.TRANSPORTATION_STATE_VALUES)[number];
    transportationType: (typeof TRANSPORTATION_TYPE_CONSTANTS.TRANSPORTATION_TYPE_VALUES)[number];
}

export interface GetTransportationDetailRet {
    code: number;
    message: string;
    data: GetTransportationDetailRetData;
}
export const getTransportationDetail = (id: string | number) => {
    return request<GetTransportationDetailRet>({
        url: "/logistics/getLogisticsDetail",
        method: "get",
        params: {
            logisticsId: id,
        },
    });
};

export interface TransportationUnloadReportProps {
    id: number | string;
    unloadLocation: string;
}
export const transportationUnloadReport = (
    props: TransportationUnloadReportProps,
) => {
    return request({
        url: "/logistics/unload",
        method: "post",
        data: props,
    });
};

export const deprecateTransportation = (id: number | string) => {
    return request({
        url: "/logistics/cancel",
        method: "post",
        data: { id },
    });
};


export interface GetProductiveProcessListData {
  // endTime?: string;
  pageIndex: number;
  pageSize: number;
  // processName?: string;
  // startTime?: string;
  /** 状态， 0正常，1作废 */
  state?: 0 | 1;
}
export const getProducList = (
  data: GetProductiveProcessListData,
) => {
  return request({
      url: "/materialPurchase/getMaterialPurchaseList",
      method: "post",
      data: data,
  });
};


export const getProducListAdd = (data: any) => {
  return request({
      url: "/materialPurchase/addMaterialPurchase",
      method: "post",
      data: data,
  });
};

// 获取地块名称

export const LandSourceService = () => {
  return request({
      url: '/land/getLandNames',
      method: 'get',
      // params: obj
  });
};
// 农作物类型

export const plantNameByLandId = (obj: any) => {
  return request({
      url: '/LandPlantBatch/getPlantNameByLandId',
      method: 'get',
      params: obj
  });
};

// 获取种植批次
export const LandIdAndPlantName = (obj: any) => {
  return request({
      url: '/LandPlantBatch/getPlantBatchByLandIdAndPlantName',
      method: 'get',
      params: obj
  });
};


// 详情

export const purchasePageDetail = (id) => {
  return request({
      url: `/materialPurchase/getMaterialPurchaseDetail?id=${id}`,
      method: 'get',
  });
};
// 收购修改

export const purchasePageEdit = (obj: any) => {
  return request({
      url: '/materialPurchase/modifyMaterialPurchase',
      method: 'post',
      data: obj
  });
};


// 查询被收购次数

export const purchaseCont = (obj: any) => {
  return request({
      url: '/materialPurchase/getPurchaseCount',
      method: 'post',
      data: obj
  });
};
// 废弃和启用
export const purchaseUpdata = (obj: any) => {
  return request({
      url: '/materialPurchase/modify-state',
      method: 'post',
      data: obj
  });
};
// 编辑
export const PurchaseBatchExists = (obj: any) => {
  return request({
      url: `/materialPurchase/isPurchaseBatchExists?purchaseBatch=${obj.purchaseBatch}`,
      method: 'get',
      // data: obj
  });
};

// 查询农户是否注册
export const checkRegister = (data: any) => {
  console.log(data,'data');
  return request({
      url: `/materialPurchase/checkFarmer`,
      method: 'post',
      data: data
  });
};

// 农户注册账号
export const registerAccount = (data: any) => {
  return request({
      url: `/user/addFarmer`,
      method: 'post',
      data: data
  });
};
// 仓储扫码
export const getPackCodeInfoByPackCodeList = (data: any) => {
  return request({
      url: `/user/addFarmer`,
      method: 'post',
      data: data
  });
};
// 仓储名称
export const selectInboundList = (data: any) => {
  return request({
      url: `/warehouse/getWarehouseInfoList`,
      method: 'get',
  });
};
// 仓储列表

export interface GetProductiveWareListData {
  // endTime?: string;
  pageIndex: number;
  pageSize: number;
  // processName?: string;
  // startTime?: string;
  /** 状态， 0正常，1作废 */
  state?: 0 | 1;
}
export const getWareList = (
  data: GetProductiveWareListData,
) => {
  return request({
      url: "/warehouse/page",
      method: "post",
      data: data,
  });
};
// 仓储新增

export const wareProcessEntry = (data: any) => {
  return request({
      url: "/warehouse/add",
      method: "post",
      data: data,
  });
};
//仓储详情
export const getWarehouseDetail = (id: number | string) => {
  return request({
      url: "/warehouse/detail",
      method: "get",
      params: {
          id: id,
      },
  });
};
// 仓储作废
export const cancelWare = (id: number | string) => {
  return request({
      url: "/warehouse/cancel",
      method: "get",
      params: {
        id: id,
    },
  });
};
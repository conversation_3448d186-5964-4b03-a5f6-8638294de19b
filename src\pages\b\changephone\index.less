//out:false
.info_box {
    height: 100%;
    // background: #F1F4EE;
    background:
        url("@/assets/imgs/bimgs/info-background1.png")no-repeat center/100% 100%,
        linear-gradient(180deg, #f1f1f1 0%, #ededed 100%);

    .back_img {
        height: 200px;
        background:
            url("@/assets/imgs/bimgs/personal-img.png")no-repeat center/100% 100%,
            linear-gradient(180deg, #f1f1f1 0%, #ededed 100%);

        .adm-nav-bar {
            padding-top: 13px;

        }
    }

    .info_div {
        width: 90%;
        height: 220px;
        border-radius: 10px;
        box-shadow: 10px;
        background: #ffff;
        margin: 0 auto;
        position: relative;
        bottom: 90px;

        .flex_wrap {
            display: grid;
            grid-template-columns: 100px 1fr;
            align-items: center;
            margin-bottom: 10px;
            box-sizing: border-box;
            padding: 0px 35px 15px 0px;

            &:first-child {
                margin-top: 10px;
            }

            // display: flex;
            // align-items: center;
            // padding: 10px 24px 0px;
            // justify-content: space-between;

            .text {
                font-size: 12px;
                justify-self: end;
            }

            .adm-input-element {
                margin: 2px 0 0 9px;
            }
        }

        .adm-form {
            --border-bottom: none !important;
            --border-top: none !important;
            padding-top: 20px;

            // .adm-list-item {
            //     .adm-list-item-content {
            //         box-shadow: inset 0 0 10px #E8EFDC;
            //         border: 1px solid #8EB249;
            //         border-radius: 8px;
            //         height: 30px;

            //         .adm-list-item-content-main {
            //             padding: 0;
            //         }
            //     }
            // }
        }

        .okbtn {
            margin: 0 auto;
            cursor: pointer;
            width: 88%;
            height: 60px;
            display: flex;
            justify-content: center;
            color: #fff;
            //  border: 1px solid;
            // background: linear-gradient(to bottom, #FFDF5F, #E27D0A);
            border-radius: 10px;
            background-image: url('@/assets/imgs/bimgs/btn1.png');
            background-size: cover;
            background-position: 100% 18%;

            // background:
            //     url("@/assets/imgs/bimgs/btn.png")no-repeat center/100% 100%,
            //     linear-gradient(180deg, #ffff 0%, #ffff 100%);
            span {
                display: inline-block;
                padding-bottom: 8px;
            }
        }

        button {
            color: #fff;
            margin-bottom: 6px;
        }

        .adm-button::before {
            --background-color: var(--adm-color-text-dark-solid) transparent;

            span {
                text-align: center;
                color: white;
            }

        }
    }
}
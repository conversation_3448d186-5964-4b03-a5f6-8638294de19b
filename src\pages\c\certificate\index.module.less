.cCertificate {
    :global {
        .main {
            flex: 1;
            padding: 20px;
            background: var(--page-bg-color);
            margin-top: 20px;
            .certificate {
                position: relative;
                width: 100%;
                height: 100%;
                background:
                    url("@/assets/imgs/c/certificate/header.png") no-repeat top/100%,
                    // url("@/assets/imgs/c/certificate/footer.png") no-repeat bottom/100% ,
                    #fff;
                display: flex;
                flex-direction: column;
                align-items: center;

                &__badge {
                    // margin-top: 29px;
                    margin-top: 5vh;

                    img {
                        width: 80px;
                        height: 77px;
                    }
                }

                &__title {
                    // margin-top: 7px;
                    margin-top: 1vh;
                    color: #333333;
                    font-weight: 500;
                    font-size: 22px;
                }

                &__content {
                    flex: 1;
                    margin-top: 4vh;
                    box-sizing: border-box;
                    width: 100%;
                    padding-bottom: 17vh;
                    display: flex;
                    flex-direction: column;
                    font-family: monospace; // 等宽字体
                    justify-content: flex-start;

                    &-item {
                        width: 100%;
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        margin-bottom: 7px;

                        &-label {
                            text-align: right;
                            width: 200px;
                            color: #333;
                            font-size: 12px;
                        }

                        &-value {
                            width: 100%;
                            word-break: break-all;
                            color: #333;
                            font-size: 12px;
                            border-radius: 4px;
                            padding: 4px;
                            &--main {
                                font-size: 18px;
                            }
                        }
                    }
                    &-custom {
                        display: flex;
                        align-items: start;
                        .certificate__content-item-value {
                            padding-top: 0;
                        }
                    }

                    .text {
                        font-size: 13px;
                        margin-top: 40px;
                        padding: 30px
                    }

                    .chapter_Icon {
                        width: 120px;
                        height: 100px;
                        position: relative;
                        /* top: 10%; */
                        left: 62%;
                        bottom: 24%;

                        img {
                            width: 100%;
                        }
                    }
                }

                &__footer {
                    position: absolute;
                    bottom: 0;
                    width: 100%;

                    img {
                        width: 100%;
                    }
                }
            }
        }
    }
}
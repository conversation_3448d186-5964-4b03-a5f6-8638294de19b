// import React from 'react'
import './index.module.less'
import { NavBar, Toast, Form, Input, Image, Button } from 'antd-mobile'

import { useNavigate } from "react-router-dom";
import {
  EyeInvisibleOutline,
  EyeOutline,
} from "antd-mobile-icons";
import request from "@/services";
import { rsaEncrypt } from "@/utils";
import { bSideRequests } from "@/services";
import { useRef, useState } from 'react';
export default () => {
  const navigate = useNavigate();
  const [visible, setVisible] = useState(false);
  const [newVisible, setnewVisible] = useState(false);
  const [repeatVisible, setrepeatVisible] = useState(false);
  let formRef = useRef<any>(null)
   
  const formValChange = () =>{
    console.log(formRef);
    
   let formInfo =  formRef.current.getFieldsValue()
    if(formRef.current&&formInfo.newPassword&&formInfo.oldPassword&&formInfo.repeatPassword){
     formRef.current.validateFields()
    }
  }
  
  const back = () => {
    navigate("/b/personalinfo");
  }
  // 修改密码
  const ok = async (info: any) => {
    const pKRet = await request({
      method: "get",
      url: "/sys-config/getPublicKey",
      data: {},
    });
    const pK = pKRet?.data?.data || "";
    let parms = {
      oldPassword: await rsaEncrypt(
        info.oldPassword,
        pK,
      ), // 旧
      newPassword: await rsaEncrypt(
        info.newPassword,
        pK,
      ),// 新
    }
    bSideRequests.modifyPassword(parms).then(res => {
      if (res.data.code === 200) {
        Toast.show(res.data.data)
        sessionStorage.clear();
        localStorage.clear();
        navigate("/b/login");

      }
    })
  }
  return (
    <div className='info_box'>
      <div className='back_img'>
        <NavBar onBack={back}>修改密码</NavBar>
      </div>
      <div className='info_div wrap_input--style'>
        <Form layout='horizontal' onFinish={ok} ref={formRef} onValuesChange={formValChange}>
          <div className='flex flex_wrap'>
            <div className='text'>旧密码</div>
            <div className='eye_style'>
              <Form.Item name='oldPassword'
                rules={[
                  // {
                  //   required: true,
                  //   message: '旧密码不能为空'
                  // },
                  () => ({
                    validator: (_, value, callback) => {
                      const regExp = new RegExp(
                        /^(?=.*[A-Z].*)(?=.*[a-z].*)(?=.*\d)(?=.*[!#$%])[A-Za-z\d!#$%]{8,20}$/
                      );
                      const verify = regExp.test(value);
                      if (!value) {
                        callback('请填写旧密码');
                      } else if (verify === false) {
                        callback('旧密码不符合密码规则！');
                      } else {
                        callback();
                      }
                    }
                  })
                ]}>
                <Input
                  type={visible ? "text" : "password"}
                />
              </Form.Item>
              <div className="eye">
                {!visible ? (
                  <EyeInvisibleOutline
                    onClick={() => setVisible(true)}
                  />
                ) : (
                  <EyeOutline
                    onClick={() => setVisible(false)}
                  />
                )}
              </div>
            </div>
          </div>
          <div className="flex flex_wrap">
            <div className='text'>新密码</div>
            <div className='eye_style'>
              <Form.Item
                name="newPassword"
                rules={[
                  // {
                  //   required: true,
                  //   message: '请填写密码'
                  // },
                  () => ({
                    validator: (_, value, callback) => {
                      const regExp = new RegExp(
                        /^(?=.*[A-Z].*)(?=.*[a-z].*)(?=.*\d)(?=.*[!#$%])[A-Za-z\d!#$%]{8,20}$/
                      );
                      const verify = regExp.test(value);
                      if (!value) {
                        callback('请填写密码');
                      } else if (verify === false) {
                        callback('新密码不符合密码规则！');
                      } else {
                        callback();
                      }
                    }
                  })
                ]}
              >
                <Input
                  className="formItem__input"
                  type={newVisible ? "text" : "password"}
                />

              </Form.Item>
              <div className="eye">
                {!newVisible ? (
                  <EyeInvisibleOutline
                    onClick={() => setnewVisible(true)}
                  />
                ) : (
                  <EyeOutline
                    onClick={() => setnewVisible(false)}
                  />
                )}
              </div>
            </div>
          </div>
          <div className='flex flex_wrap'>
            <div className='text'>确认密码</div>
            <div className='eye_style'>
              <Form.Item name='repeatPassword' rules={[
                {
                  required: true,
                  message: '确认密码不能为空'
                },
                ({ getFieldValue }) => ({
                  validator(rule, value) {
                    if (!value || getFieldValue('newPassword') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject('两次密码输入不一致！');
                  }
                })
              ]}>
                <Input type={repeatVisible ? "text" : "password"} />
              </Form.Item>
              <div className="eye">
                {!repeatVisible ? (
                  <EyeInvisibleOutline
                    onClick={() => setrepeatVisible(true)}
                  />
                ) : (
                  <EyeOutline
                    onClick={() => setrepeatVisible(false)}
                  />
                )}
              </div>
            </div>

          </div>
          <div className='okbtn'>
            <Button color='primary' style={{ width: '100%', height: '100%' }} fill='none' type="submit">
              确认
            </Button>
          </div>
        </Form>
      </div>
    </div>
  )
}

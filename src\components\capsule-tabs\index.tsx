import { CapsuleTabs as AntdCapsuleTabs } from "antd-mobile";
import { LeftOutline } from "antd-mobile-icons";
import { useNavigate } from "react-router-dom";

import styles from "./index.module.less";

import type { CapsuleTabsProps } from "antd-mobile";

interface ICapsuleTabsProps extends CapsuleTabsProps {}
/**
 * docs: https://mobile.ant.design/zh/components/capsule-tabs
 */
const CapsuleTabs = (props: ICapsuleTabsProps) => {
    return (
        <div className={styles.capsuleTabs}>
            <AntdCapsuleTabs {...props}></AntdCapsuleTabs>
        </div>
    );
};
CapsuleTabs.Tab = AntdCapsuleTabs.Tab;

export default CapsuleTabs;

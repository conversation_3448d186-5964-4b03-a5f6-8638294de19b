import { Toast } from "antd-mobile";

import { BOX_CODE_STATE_ERR } from "@/config";

import request from "./request";

export * as bSideRequests from "./modules/b";
export * as cSideRequests from "./modules/c";

import type { AxiosResponse } from "axios";

export default request;

//文件上传接口
export const getUploadUrl = async (fileName: string) => {
    const { data } = await request({
        method: "get",
        url: "/minio/get-url",
        params: {
            fileName,
        },
    });
    return data;
};

export interface GetPackCodeInfoByPackCodeRetData {
    id: number;
    boxCode: string;
    state: number;
    productName: string;
}
export interface GetPackCodeInfoByPackCodeRet {
    code: number;
    message: string;
    data: GetPackCodeInfoByPackCodeRetData;
}
export enum BoxCodeVerifyModule {
    "OUT",
    "IN",
    "PACK",
    "LOGISTICS",
}
interface getPackCodeInfoByPackCodePrams {
    boxCode: string;
    optState: BoxCodeVerifyModule;
}
export const getPackCodeInfoByPackCode = (
    params: getPackCodeInfoByPackCodePrams,
    shouldActive = true,
) => {
    return request<GetPackCodeInfoByPackCodeRet>({
        url: "/box/product",
        method: "post",
        data: params,
    }).then((boxInfoRet) => {
        return new Promise<typeof boxInfoRet>((resolve, reject) => {
            const boxInfo = boxInfoRet?.data?.data || {};
            const stateMap = {
                0: "未激活",
                1: "已激活",
                2: "已作废",
            } as any;
            const expectState = shouldActive ? 1 : 0;
            if (expectState !== boxInfo.state) {
                Toast.show({
                    content: `箱码${stateMap[boxInfo.state]}`,
                });
                reject({
                    type: BOX_CODE_STATE_ERR,
                    message: `箱码${stateMap[boxInfo.state]}`,
                });
            } else {
                resolve(boxInfoRet);
            }
        });
    });
};

export const getTraceCodeInfoByTraceCodeId = (id: string) => {
    return request({
        url: "/trace-code/product",
        method: "get",
        params: {
            id,
        },
        // @ts-ignore
        __disableGlobalToast: true,
    });
};
interface getPackCodeInfoByPackCodePramsList {
    id: string;
    type?: number;
}
export const getPackCodeInfoByPackCodeList = (
    params: getPackCodeInfoByPackCodePramsList,
    shouldActive = true,
) => {
    return request<GetPackCodeInfoByPackCodeRet>({
        url: "/trace-code/trace/data",
        method: "get",
        params: params,
    }).then((boxInfoRet) => {
        return new Promise<typeof boxInfoRet>((resolve, reject) => {
            const boxInfo = boxInfoRet?.data?.data || {};
            resolve(boxInfoRet);
            // const stateMap = {
            //     0: "未激活",
            //     1: "已激活",
            //     2: "已作废",
            // } as any;
            // const expectState = shouldActive ? 1 : 0;
            // if (expectState !== boxInfo.state) {
            //     Toast.show({
            //         content: `箱码${stateMap[boxInfo.state]}`,
            //     });
            //     reject({
            //         type: BOX_CODE_STATE_ERR,
            //         message: `箱码${stateMap[boxInfo.state]}`,
            //     });
            // } else {
            //     resolve(boxInfoRet);
            // }
        });
    });
};

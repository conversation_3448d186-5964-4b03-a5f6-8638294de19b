import { Button, Modal, Toast } from "antd-mobile";

import type { ButtonProps, ToastShowProps } from "antd-mobile";
import type { ToastHandler } from "antd-mobile/es/components/toast";

import styles from "./index.module.less";
import rsaDecrypte from "./rsa-dec";

export { default as regexps } from "./regexps";

export { defineConstants } from "./define-constants";

export * from "./encrypt";

export interface ShowConfirmModalProps {
    title: string;
    content?: string;
    confirmText?: string;
    cancelText?: string;
    onConfirm?: () => void;
    onCancel?: () => void;
    confirmBtnProps?: ButtonProps;
    cancelBtnProps?: ButtonProps;
}
export const showConfirmModal = (props: ShowConfirmModalProps) => {
    const {
        title,
        content,
        confirmText = "确定",
        cancelText = "取消",
        onConfirm,
        onCancel,
        confirmBtnProps,
        cancelBtnProps,
    } = props;
    const handler = Modal.show({
        title: title,
        bodyClassName: styles.confirmModal,
        content: (
            <div>
                {content && <div className="content">{content}</div>}
                <div className="actions">
                    <Button
                        fill="none"
                        onClick={() => {
                            onCancel?.();
                            handler.close();
                        }}
                        {...cancelBtnProps}
                    >
                        {cancelText}
                    </Button>
                    <Button
                        fill="none"
                        color="primary"
                        onClick={() => {
                            onConfirm?.();
                            handler.close();
                        }}
                        {...confirmBtnProps}
                    >
                        {confirmText}
                    </Button>
                </div>
            </div>
        ),
    });
    return handler;
};

export type ToastLoadingHandler = ToastHandler;
export const showToastLoading = (showToastLoadingProps?: ToastShowProps) => {
    const {
        content = "加载中…",
        maskStyle,
        ...restProps
    } = showToastLoadingProps || {};
    return Toast.show({
        maskStyle: {
            background: "rgba(0,0,0,.2)",
            ...maskStyle,
        },
        icon: "loading",
        content: content,
        maskClassName: styles.ToastLoading,
        duration: 3,
        ...restProps,
    });
};

export const getVideoCover = (file: File) => {
    return new Promise((resolve) => {
        const canvas = document.createElement("canvas");
        const videoUrl = URL.createObjectURL(file);
        const videoElement = document.createElement("video");
        videoElement.src = videoUrl;
        // videoElement.autoplay = true;
        videoElement.muted = true;
        videoElement.playsInline = true;
        videoElement.preload = "auto";
        videoElement
            .play()
            .then(() => {
                videoElement.pause();
            })
            .catch((err) => {
                console.log("video play error", err);
                resolve("/get-poster-err-fallback.png");
            });

        videoElement.onloadeddata = () => {
            console.log("onloadeddata call");
        };

        videoElement.addEventListener("loadeddata", async function (_event) {
            console.log("add loadeddata call");
            const canvas = document.createElement("canvas");
            canvas.width = videoElement.videoWidth;
            canvas.height = videoElement.videoHeight;
            const ctx = canvas.getContext("2d");
            ctx?.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
            resolve(canvas.toDataURL("image/png"));
        });
    });
};

export function hidePhoneNumber(phoneNumber: string): string {
    const hiddenPhoneNumber =
        phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(7);
    return hiddenPhoneNumber;
}

export const urltoChange = (url: string) => {
    const index1 = url.indexOf("png");
    const index2 = url.indexOf("?");
    if (index2 > 0) {
        url = url.slice(0, index2);
    } else {
        url = url.slice(0, index1 + 3);
    }
    return url;
};
// 处理加密URL
export const decryptedUrl = async (str: string) => {
    if (!str) {
        return "";
    }
    const index = str.indexOf("?");
    const url = str.slice(0, index);
    // const url=str.split("?")[0]
    console.log("decryptedUrl", url);
    const det = await rsaDecrypte(url);
    console.log("det", det);
    return det + str.slice(index, str.length);
};

export const isArrayArr = (arr: any[]) => {
    if (Array.isArray(arr) && arr.length > 0) {
        return arr;
    }
    return [];
};

export const formatArrNull = (arr: any) => {
    let newArr: any[] = [];
    arr.forEach((item: any) => {
        if (item) {
            newArr.push(item);
        }
    });
    return newArr;
};

//decryptStr 处理加密串
export const decryptStr = async (str: string) => {
    if (str) {
        const det = await rsaDecrypte(str);
        console.log("det--str", det);
        return det;
    }
    return "";
};
export const getParamUsingURLSearchParams = (
    queryString: any,
    paramName: any,
) => {
    const params = new URLSearchParams(queryString);
    return params.get(paramName);
};

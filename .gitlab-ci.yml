image: docker:latest

stages:
    - build

job_deploy:
    stage: build
    services:
        - docker:dind
    only:
        - dev
    before_script:
        - echo 'docker deploy'
    script:
        - docker build -t cm-ut-h5 -f test.Dockerfile .
        - docker save cm-ut-h5 -o cm-ut-h5.tar
        - tar -czf cm-ut-h5.tar.gz cm-ut-h5.tar
        - ls -a
        - eval $(ssh-agent -s)
        - echo "$SSH_PRIVATE_KEY" > ~/deploy.key
        - chmod 0600 ~/deploy.key
        - ssh-add ~/deploy.key
        - mkdir -p ~/.ssh
        - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
        - ls -a
        - scp -oPort=17382 -c aes128-ctr -r cm-ut-h5.tar.gz $DEV_SERVER_USER@$DEV_SERVER_HOST:/mnt/data/home/<USER>/cm/cm-ut-h5
        - ssh -oPort=17382 $DEV_SERVER_USER@$DEV_SERVER_HOST "cd /mnt/data/home/<USER>/cm/cm-ut-h5 && tar -xzf cm-ut-h5.tar.gz && docker images && docker stop cm-ut-h5 || true && docker rm cm-ut-h5 || true && docker load -i cm-ut-h5.tar && docker run --name cm-ut-h5 -p 8016:80 -p 8018:443 -e 'APISERVER=http://***************:9022' -e ssl_certificate=$ssl_certificate -e ssl_certificate_key=$ssl_certificate_key -d cm-ut-h5 && rm cm-ut-h5.tar.gz"
    tags:
        - WebDockerRunner
    retry: 0

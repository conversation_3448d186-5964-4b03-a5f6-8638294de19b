.ScanPack {
    margin: 10px;
    padding: 20px 16px;
    padding-bottom: 58px;
    background: var(--page-block-color);
    border-radius: var(--border-radius);
    :global {
        .scanBoxRetContainer {
            border-bottom: 1px solid var(--support-color);
            padding-bottom: 10px;
            .scanRetHeader {
                color: var(--th-text-color);
                font-size: var(--text-md);
                font-weight: bold;
                margin-bottom: 18px;
            }
            .scanRet {
                display: flex;
                justify-content: space-between;
                &__code {
                    flex: 1;
                    word-break: break-all;
                    color: var(--text-secondary);
                    font-size: var(--text-sm);
                }
            }
            .deleteBtn {
                // margin: 0 auto;
                text-align: right;
                padding: 0 10px;
                font-size: var(--text-sm);
            }
            .scanBtn {
                box-sizing: content-box;
                margin: 0 auto;
                margin-top: 20px;
                padding: 6px 5px;
                border-radius: calc(var(--border-radius) / 2);
                width: 91px;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: var(--text-sm);
                line-height: 1;
                & > span {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }
                .scanBtnIcon {
                    font-size: 16px;
                }
            }
        }

        .outTable-extraPadding {
            padding-left: 14px;
            padding-right: 14px;
        }
    }
}

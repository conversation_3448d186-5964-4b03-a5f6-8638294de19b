import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
    Button,
    Input,
    TextArea,
    Picker,
    Space,
    Toast,
    DotLoading,
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
    UpOutline,
    DownOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import { useRequest, useSetState } from "ahooks";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";
import CollapseTable from "@/components/collapse-table";

import iconScanQrCode from "@/assets/imgs/scan-qrcode.png";

import { showConfirmModal } from "@/utils";
import { bSideRequests } from "@/services";
import {
    TRANSPORTATION_STATE_CONSTANTS,
    TRANSPORTATION_TYPE_CONSTANTS,
} from "@/config";

import styles from "./index.module.less";

export default () => {
    const navigate = useNavigate();
    const { id } = useParams();

    const getTransportationDetailRequest = useRequest(
        () => {
            if (!id) {
                throw new Error("缺少id");
            }
            return bSideRequests.getTransportationDetail(id);
        },
        {
            refreshDeps: [id],
        },
    );
    const transportationDetail: Partial<bSideRequests.GetTransportationDetailRetData> =
        getTransportationDetailRequest.data?.data?.data || {};

    const deprecateTransportationRequest = useRequest(
        () => {
            if (!id) {
                throw new Error("缺少id");
            }
            return bSideRequests.deprecateTransportation(id);
        },
        {
            manual: true,
            onSuccess() {
                Toast.show({
                    icon: "success",
                    content: "作废成功",
                });
                getTransportationDetailRequest.refresh();
            },
        },
    );

    if (getTransportationDetailRequest.loading) {
        return (
            <div
                style={{
                    paddingTop: "50px",
                    textAlign: "center",
                    color: "var(--adm-color-weak)",
                }}
            >
                <div style={{ fontSize: 24, marginBottom: 24 }}>
                    <DotLoading />
                </div>
                正在加载数据
            </div>
        );
    }

    return (
        <div className={`${styles.inboundLogDetail}`}>
            <Form layout="horizontal">
                <List>
                    <List.Item extra={transportationDetail?.transNumber || "-"}>
                        运输单号
                    </List.Item>
                    <List.Item
                        extra={transportationDetail?.loEnterprises || "-"}
                    >
                        物流企业
                    </List.Item>
                    <List.Item extra={transportationDetail?.loNumber || "-"}>
                        物流单号
                    </List.Item>
                </List>
                <div className="boxCodeList">
                    <CollapseTable
                        columns={[
                            {
                                title:
                                    "箱码" +
                                    (transportationDetail?.boxTo?.length &&
                                    transportationDetail.boxTo.length > 0
                                        ? `（共${transportationDetail.boxTo.length}个）`
                                        : ""),
                                dataIndex: "boxCode",
                                width: "50%",
                            },
                            {
                                title: "产品名称",
                                dataIndex: "productName",
                                width: "40%",
                            },
                            {
                                width: "10%",
                            },
                        ]}
                        dataSource={transportationDetail?.boxTo || []}
                    ></CollapseTable>
                </div>

                {transportationDetail.state !== undefined &&
                    TRANSPORTATION_STATE_CONSTANTS.TRANSPORTATION_STATE_VK[
                        transportationDetail.state
                    ] === "SHIPPING" && (
                        <Space
                            direction="vertical"
                            style={{
                                width: "100%",
                                marginTop: 40,
                                "--gap": "12px",
                            }}
                        >
                            <Button
                                className={`${styles.Btn}`}
                                loading={deprecateTransportationRequest.loading}
                                onClick={() => {
                                    showConfirmModal({
                                        title: "确认作废",
                                        content: "确认作废吗",
                                        confirmBtnProps: {
                                            color: "danger",
                                        },
                                        onConfirm() {
                                            deprecateTransportationRequest.run();
                                        },
                                    });
                                }}
                                color="primary"
                                fill="outline"
                                shape="rounded"
                                block
                            >
                                作废
                            </Button>
                        </Space>
                    )}
            </Form>
        </div>
    );
};

import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
    Button,
    Input,
    TextA<PERSON>,
    Picker,
    Space,
    DotLoading,
    Ellipsis,
} from "antd-mobile";
import {
    UnorderedListOutline,
    PayCircleOutline,
    SetOutline,
    UpOutline,
    DownOutline,
} from "antd-mobile-icons";
import dayjs from "dayjs";
import { useRequest, useSetState } from "ahooks";

import NavBar from "@/components/nav-bar";
import CapsuleTabs from "@/components/capsule-tabs";
import List from "@/components/list";
import Form from "@/components/form";
import CollapseTable from "@/components/collapse-table";

import iconScanQrCode from "@/assets/imgs/scan-qrcode.png";

import { bSideRequests } from "@/services";

import styles from "./index.module.less";

export default () => {
    const navigate = useNavigate();
    const { id } = useParams();

    const getOutboundDetailRequest = useRequest(
        () => {
            if (!id) {
                throw new Error("缺少id");
            }
            return bSideRequests.getOutboundDetail(id);
        },
        {
            refreshDeps: [id],
        },
    );
    const outboundDetail = getOutboundDetailRequest.data?.data?.data || {};

    if (getOutboundDetailRequest.loading) {
        return (
            <div
                style={{
                    paddingTop: "50px",
                    textAlign: "center",
                    color: "var(--adm-color-weak)",
                }}
            >
                <div style={{ fontSize: 24, marginBottom: 24 }}>
                    <DotLoading />
                </div>
                正在加载数据
            </div>
        );
    }

    return (
        <div className={`${styles.inboundLogDetail}`}>
            <List>
                <List.Item
                    extra={
                        <Ellipsis
                            content={outboundDetail?.outWarehouseNumber || "-"}
                        ></Ellipsis>
                    }
                >
                    出库单号
                </List.Item>
                <List.Item extra={outboundDetail?.dealer || "-"}>
                    经销商
                </List.Item>
                <List.Item
                    extra={
                        outboundDetail?.provinces?.length > 0
                            ? outboundDetail.provinces
                                  .map((item: any) => item.province)
                                  .join("，")
                            : "-"
                    }
                >
                    经销区域
                </List.Item>
                <List.Item extra={outboundDetail?.salesChannels || "-"}>
                    销售渠道
                </List.Item>
                <List.Item
                    extra={
                        <Ellipsis
                            content={outboundDetail?.orderNumber || "-"}
                        ></Ellipsis>
                    }
                >
                    订单号
                </List.Item>
                <List.Item extra={outboundDetail?.logisticsEnterprises || "-"}>
                    物流企业
                </List.Item>
                <List.Item
                    extra={
                        <Ellipsis
                            content={outboundDetail?.logisticsNumber || "-"}
                        ></Ellipsis>
                    }
                >
                    物流单号
                </List.Item>
            </List>
            <div className="boxCodeList">
                <CollapseTable
                    columns={[
                        {
                            title:
                                "箱码" +
                                (outboundDetail?.boxTo?.length &&
                                outboundDetail.boxTo.length > 0
                                    ? `（共${outboundDetail.boxTo.length}个）`
                                    : ""),
                            dataIndex: "boxCode",
                            width: "50%",
                        },
                        {
                            title: "产品名称",
                            dataIndex: "productName",
                            width: "40%",
                        },
                        {
                            width: "10%",
                        },
                    ]}
                    dataSource={outboundDetail?.boxTo || []}
                ></CollapseTable>
            </div>
        </div>
    );
};

import React, { useEffect, useState } from "react";
import {
    create<PERSON><PERSON><PERSON><PERSON><PERSON>er,
    RouterProvider,
    Navigate,
    Outlet,
    useOutlet,
    useOutletContext,
    useLocation,
    useNavigate,
    Link,
} from "react-router-dom";
import { Badge } from "antd-mobile";

import NavBar from "@/components/nav-bar";
import TabBar, { RouteTabBar } from "@/components/tab-bar";
import CacheRouteContainer from "@/components/cache-route-container";

import type { RouteObject } from "react-router-dom";

import iconOutbound from "@/assets/imgs/tabs/outbound.png";
import iconOutboundSelected from "@/assets/imgs/tabs/outbound-selected.png";
import iconLog from "@/assets/imgs/tabs/log.png";
import iconLogSelected from "@/assets/imgs/tabs/log-selected.png";

import { ReactComponent as LogIcon } from "@/assets/imgs/tabs/svg/log.svg";
import { ReactComponent as OutboundIcon } from "@/assets/imgs/tabs/svg/outbound.svg";

import styles from "./index.module.less";

import BScanOutbound from "@/pages/b/outbound/scan-outbound";
import BOutboundList from "@/pages/b/outbound/log-list";
import BOutboundLogDetail from "@/pages/b/outbound/outbound-log-detail";
import BReportErrorResult from "@/pages/b/report-error-result";
import NoPermissionPage from "@/pages/403";

import useStore from "@/store";

const PackLayout = () => {
    const [pageTitle, setPageTitle] = useState("");
    const userMenuPermission = useStore((state) => state.userMenuPermission);

    const tabs = [
        {
            key: "scan",
            title: "扫码出库",
            icon: (active: boolean) => (
                // active ? <img src={iconOutboundSelected} /> : <img src={iconOutbound} />,
                <OutboundIcon
                    style={{
                        color: active ? "var(--primary-color)" : "#e7e7e7",
                    }}
                ></OutboundIcon>
            ),
        },
        {
            key: "log",
            title: "记录查看",
            icon: (active: boolean) => (
                // active ? <img src={iconLogSelected} /> : <img src={iconLog} />,
                <LogIcon
                    style={{
                        color: active ? "var(--primary-color)" : "#e7e7e7",
                    }}
                ></LogIcon>
            ),
        },
    ];

    if (!userMenuPermission.find((item) => item.perms === "out")) {
        return (
            <div className={`_global_page ${styles.layout}`}>
                <NavBar>{pageTitle}</NavBar>
                <NoPermissionPage></NoPermissionPage>
            </div>
        );
    }

    return (
        <div className={`_global_page ${styles.layout}`}>
            <NavBar>{pageTitle}</NavBar>
            <div className="_global_pageScrollContent">
                <Outlet context={{ setPageTitle }}></Outlet>
            </div>
            <RouteTabBar tabs={tabs} prefix="/b/home/<USER>"></RouteTabBar>
        </div>
    );
};

interface IPackPageWrapperProps {
    title?: string;
    children: React.ReactNode;
}
const PackPageWrapper = ({ children, title = "" }: IPackPageWrapperProps) => {
    const { setPageTitle } = useOutletContext<any>();
    useEffect(() => {
        setPageTitle(title);
    }, [title]);

    return children;
};

export default {
    path: "outbound",
    element: <PackLayout></PackLayout>,
    children: [
        {
            index: true,
            element: <Navigate to="scan" replace></Navigate>,
        },
        {
            path: "scan",
            element: <CacheRouteContainer></CacheRouteContainer>,
            children: [
                {
                    index: true,
                    element: (
                        <PackPageWrapper title="扫码出库">
                            <BScanOutbound></BScanOutbound>
                        </PackPageWrapper>
                    ),
                },
                {
                    path: "result",
                    element: (
                        <PackPageWrapper title="扫码出库">
                            <BReportErrorResult></BReportErrorResult>
                        </PackPageWrapper>
                    ),
                },
            ],
        },
        {
            path: "log",
            children: [
                {
                    index: true,
                    element: (
                        <PackPageWrapper title="出库列表">
                            <BOutboundList></BOutboundList>
                        </PackPageWrapper>
                    ),
                },
                {
                    path: ":id",
                    element: (
                        <PackPageWrapper title="出库详情">
                            <BOutboundLogDetail></BOutboundLogDetail>
                        </PackPageWrapper>
                    ),
                },
            ],
        },
    ],
} as RouteObject;
